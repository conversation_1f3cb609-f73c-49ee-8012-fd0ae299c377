<template>
  <view class="container">
    <view class="title">Iconfont 图标测试</view>
    
    <!-- 测试 iconfont 图标 -->
    <view class="section">
      <view class="section-title">iconfont 图标测试</view>
      <view class="icon-row">
        <view class="icon-item">
          <text class="iconfont icon-arrow-right"></text>
          <text class="icon-name">arrow-right</text>
        </view>
        <view class="icon-item">
          <text class="iconfont icon-calendar"></text>
          <text class="icon-name">calendar</text>
        </view>
        <view class="icon-item">
          <text class="iconfont icon-comment"></text>
          <text class="icon-name">comment</text>
        </view>
        <view class="icon-item">
          <text class="iconfont icon-file"></text>
          <text class="icon-name">file</text>
        </view>
      </view>
    </view>

    <!-- 测试 iconfonts 图标 -->
    <view class="section">
      <view class="section-title">iconfonts 图标测试</view>
      <view class="icon-row">
        <view class="icon-item">
          <text class="iconfonts icon-tingchejilu2"></text>
          <text class="icon-name">tingchejilu2</text>
        </view>
        <view class="icon-item">
          <text class="iconfonts icon-tingchechang"></text>
          <text class="icon-name">tingchechang</text>
        </view>
        <view class="icon-item">
          <text class="iconfonts icon-wodecheliang"></text>
          <text class="icon-name">wodecheliang</text>
        </view>
        <view class="icon-item">
          <text class="iconfonts icon-shouye"></text>
          <text class="icon-name">shouye</text>
        </view>
      </view>
    </view>

    <!-- 字体路径测试 -->
    <view class="section">
      <view class="section-title">字体加载状态</view>
      <view class="font-test">
        <text class="test-text iconfont">如果看到方块，说明字体未加载</text>
        <text class="test-text iconfonts">如果看到方块，说明字体未加载</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('测试页面已加载')
  
  // 检查字体是否加载
  setTimeout(() => {
    const iconfontElements = document.querySelectorAll('.iconfont')
    const iconfontsElements = document.querySelectorAll('.iconfonts')
    
    console.log('iconfont 元素数量:', iconfontElements.length)
    console.log('iconfonts 元素数量:', iconfontsElements.length)
    
    // 检查计算样式
    if (iconfontElements.length > 0) {
      const computedStyle = window.getComputedStyle(iconfontElements[0])
      console.log('iconfont 字体族:', computedStyle.fontFamily)
    }
  }, 1000)
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 20rpx;
}

.icon-row {
  display: flex;
  flex-wrap: wrap;
  gap: 30rpx;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 150rpx;
  padding: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  background: #fafafa;
}

.icon-item text:first-child {
  font-size: 48rpx;
  color: #c20000;
  margin-bottom: 10rpx;
}

.icon-name {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  word-break: break-all;
}

.font-test {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-text {
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  background: #f9f9f9;
  font-size: 28rpx;
}

/* 确保样式正确应用 */
.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
