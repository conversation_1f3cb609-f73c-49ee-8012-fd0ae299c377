<template>
  <view class="container">
    <view class="title">Iconfont 图标测试</view>

    <!-- 基础测试 -->
    <view class="basic-test">
      <view class="test-section">
        <text class="section-title">基础字体测试</text>
        <view class="test-row">
          <text class="test-label">普通文字:</text>
          <text class="normal-text">这是普通文字</text>
        </view>
        <view class="test-row">
          <text class="test-label">iconfont字体:</text>
          <text class="iconfont test-font">这是iconfont字体</text>
        </view>
        <view class="test-row">
          <text class="test-label">iconfonts字体:</text>
          <text class="iconfonts test-font">这是iconfonts字体</text>
        </view>
      </view>

      <view class="test-section">
        <text class="section-title">图标测试</text>
        <view class="icon-test-grid">
          <view class="icon-test-item">
            <text class="iconfont icon-arrow-right large-icon"></text>
            <text class="icon-desc">arrow-right</text>
          </view>
          <view class="icon-test-item">
            <text class="iconfont icon-calendar large-icon"></text>
            <text class="icon-desc">calendar</text>
          </view>
          <view class="icon-test-item">
            <text class="iconfonts icon-wodecheliang large-icon"></text>
            <text class="icon-desc">wodecheliang</text>
          </view>
          <view class="icon-test-item">
            <text class="iconfonts icon-shouye large-icon"></text>
            <text class="icon-desc">shouye</text>
          </view>
        </view>
      </view>

      <view class="test-section">
        <text class="section-title">Unicode测试</text>
        <view class="unicode-test">
          <text class="iconfont unicode-icon">&#xe665;</text>
          <text class="icon-desc">Unicode: e665</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('测试页面已加载')

  // 检查字体是否加载成功
  setTimeout(() => {
    // #ifdef H5
    if (typeof document !== 'undefined') {
      const testElement = document.querySelector('.iconfont')
      if (testElement) {
        const computedStyle = window.getComputedStyle(testElement)
        console.log('iconfont 字体族:', computedStyle.fontFamily)
        console.log('字体大小:', computedStyle.fontSize)
      }
    }
    // #endif
  }, 1000)
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.basic-test {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.test-section {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.test-row {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.test-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666;
}

.normal-text {
  font-size: 28rpx;
  color: #333;
}

.test-font {
  font-size: 28rpx;
  color: #c20000;
}

.icon-test-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.icon-test-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 150rpx;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
  border: 2rpx solid #eee;
}

.large-icon {
  font-size: 60rpx;
  color: #c20000;
  margin-bottom: 10rpx;
  line-height: 1;
}

.unicode-icon {
  font-size: 60rpx;
  color: #c20000;
  margin-right: 20rpx;
}

.unicode-test {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.icon-desc {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  word-break: break-all;
}

/* 强制应用字体样式 */
.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal !important;
  font-weight: normal !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  vertical-align: middle;
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-style: normal !important;
  font-weight: normal !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  vertical-align: middle;
}

/* 图标定义 - 确保在当前作用域生效 */
.icon-arrow-right:before { content: '\e665'; }
.icon-calendar:before { content: '\e667'; }
.icon-wodecheliang:before { content: "\e615"; }
.icon-shouye:before { content: "\e639"; }
</style>
