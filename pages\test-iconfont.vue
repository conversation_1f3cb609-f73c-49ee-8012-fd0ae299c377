<template>
  <view class="container">
    <view class="title">Iconfont 图标测试</view>

    <!-- 使用测试组件 -->
    <icon-test />

    <!-- 直接测试 -->
    <view class="direct-test">
      <view class="test-item">
        <text class="iconfont icon-arrow-right test-icon"></text>
        <text>直接测试 iconfont</text>
      </view>
      <view class="test-item">
        <text class="iconfonts icon-wodecheliang test-icon"></text>
        <text>直接测试 iconfonts</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'
import IconTest from '@/components/icon-test/icon-test.vue'

onMounted(() => {
  console.log('测试页面已加载')
})
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 40rpx;
  color: #333;
}

.direct-test {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-top: 20rpx;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  background: #fafafa;
}

.test-icon {
  font-size: 40rpx;
  color: #c20000;
  margin-right: 20rpx;
}

/* 确保样式正确应用 */
.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
