import{af as t,o as e,c as s,w as i,e as o,n as a,k as l,ai as r,r as n,b as u,f as c,s as d,g as h,j as b,t as m,l as f,d as g,m as p,a as _,h as y,F as x,i as k,x as v,V as C,ad as S,L as w}from"./index-4d380fda.js";import{_ as B}from"./uni-icons.3c47aaf1.js";import{_ as I}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as T}from"./u-icon.66912310.js";const $=t=>"number"==typeof t?t+"px":t;const R=I({name:"UniNavBar",components:{statusBar:I({name:"UniStatusBar",data:()=>({statusBarHeight:t().statusBarHeight+"px"})},[["render",function(t,r,n,u,c,d){const h=l;return e(),s(h,{style:a({height:c.statusBarHeight}),class:"uni-status-bar"},{default:i((()=>[o(t.$slots,"default",{},void 0,!0)])),_:3},8,["style"])}],["__scopeId","data-v-0bfbbc2d"]])},emits:["clickLeft","clickRight","clickTitle"],props:{dark:{type:Boolean,default:!1},title:{type:String,default:""},leftText:{type:String,default:""},rightText:{type:String,default:""},leftIcon:{type:String,default:""},rightIcon:{type:String,default:""},fixed:{type:[Boolean,String],default:!1},color:{type:String,default:""},backgroundColor:{type:String,default:""},statusBar:{type:[Boolean,String],default:!1},shadow:{type:[Boolean,String],default:!1},border:{type:[Boolean,String],default:!0},height:{type:[Number,String],default:44},leftWidth:{type:[Number,String],default:60},rightWidth:{type:[Number,String],default:60},stat:{type:[Boolean,String],default:""}},computed:{themeBgColor(){return this.dark?this.backgroundColor?this.backgroundColor:this.dark?"#333":"#FFF":this.backgroundColor||"#FFF"},themeColor(){return this.dark?this.color?this.color:this.dark?"#fff":"#333":this.color||"#333"},navbarHeight(){return $(this.height)},leftIconWidth(){return $(this.leftWidth)},rightIconWidth(){return $(this.rightWidth)}},mounted(){uni.report&&this.stat&&""!==this.title&&uni.report("title",this.title)},methods:{onClickLeft(){this.$emit("clickLeft")},onClickRight(){this.$emit("clickRight")},onClickTitle(){this.$emit("clickTitle")}}},[["render",function(t,g,p,_,y,x){const k=r("status-bar"),v=n(u("uni-icons"),B),C=l,S=f;return e(),s(C,{class:d(["uni-navbar",{"uni-dark":p.dark,"uni-nvue-fixed":p.fixed}])},{default:i((()=>[c(C,{class:d(["uni-navbar__content",{"uni-navbar--fixed":p.fixed,"uni-navbar--shadow":p.shadow,"uni-navbar--border":p.border}]),style:a({"background-color":x.themeBgColor,"border-bottom-color":x.themeColor})},{default:i((()=>[p.statusBar?(e(),s(k,{key:0})):h("",!0),c(C,{style:a({color:x.themeColor,backgroundColor:x.themeBgColor,height:x.navbarHeight}),class:"uni-navbar__header"},{default:i((()=>[c(C,{onClick:x.onClickLeft,class:"uni-navbar__header-btns uni-navbar__header-btns-left",style:a({width:x.leftIconWidth})},{default:i((()=>[o(t.$slots,"left",{},(()=>[p.leftIcon.length>0?(e(),s(C,{key:0,class:"uni-navbar__content_view"},{default:i((()=>[c(v,{color:x.themeColor,type:p.leftIcon,size:"20"},null,8,["color","type"])])),_:1})):h("",!0),p.leftText.length?(e(),s(C,{key:1,class:d([{"uni-navbar-btn-icon-left":!p.leftIcon.length>0},"uni-navbar-btn-text"])},{default:i((()=>[c(S,{style:a({color:x.themeColor,fontSize:"12px"})},{default:i((()=>[b(m(p.leftText),1)])),_:1},8,["style"])])),_:1},8,["class"])):h("",!0)]),!0)])),_:3},8,["onClick","style"]),c(C,{class:"uni-navbar__header-container",onClick:x.onClickTitle},{default:i((()=>[o(t.$slots,"default",{},(()=>[p.title.length>0?(e(),s(C,{key:0,class:"uni-navbar__header-container-inner"},{default:i((()=>[c(S,{class:"uni-nav-bar-text uni-ellipsis-1",style:a({color:x.themeColor})},{default:i((()=>[b(m(p.title),1)])),_:1},8,["style"])])),_:1})):h("",!0)]),!0)])),_:3},8,["onClick"]),c(C,{onClick:x.onClickRight,class:"uni-navbar__header-btns uni-navbar__header-btns-right",style:a({width:x.rightIconWidth})},{default:i((()=>[o(t.$slots,"right",{},(()=>[p.rightIcon.length?(e(),s(C,{key:0},{default:i((()=>[c(v,{color:x.themeColor,type:p.rightIcon,size:"22"},null,8,["color","type"])])),_:1})):h("",!0),p.rightText.length&&!p.rightIcon.length?(e(),s(C,{key:1,class:"uni-navbar-btn-text"},{default:i((()=>[c(S,{class:"uni-nav-bar-right-text",style:a({color:x.themeColor})},{default:i((()=>[b(m(p.rightText),1)])),_:1},8,["style"])])),_:1})):h("",!0)]),!0)])),_:3},8,["onClick","style"])])),_:3},8,["style"])])),_:3},8,["class","style"]),p.fixed?(e(),s(C,{key:0,class:"uni-navbar__placeholder"},{default:i((()=>[p.statusBar?(e(),s(k,{key:0})):h("",!0),c(C,{class:"uni-navbar__placeholder-view",style:a({height:x.navbarHeight})},null,8,["style"])])),_:1})):h("",!0)])),_:3},8,["class"])}],["__scopeId","data-v-d8073948"]]);const E=I({name:"u-subsection",mixins:[p,_,{props:{list:{type:Array,default:g.subsection.list},current:{type:[String,Number],default:g.subsection.current},activeColor:{type:String,default:g.subsection.activeColor},inactiveColor:{type:String,default:g.subsection.inactiveColor},mode:{type:String,default:g.subsection.mode},fontSize:{type:[String,Number],default:g.subsection.fontSize},bold:{type:Boolean,default:g.subsection.bold},bgColor:{type:String,default:g.subsection.bgColor},keyName:{type:String,default:g.subsection.keyName}}}],data:()=>({itemRect:{width:0,height:0}}),watch:{list(t,e){this.init()},current:{immediate:!0,handler(t){}}},computed:{wrapperStyle(){const t={};return"button"===this.mode&&(t.backgroundColor=this.bgColor),t},barStyle(){const t={};return t.width=`${this.itemRect.width}px`,t.height=`${this.itemRect.height}px`,t.transform=`translateX(${this.current*this.itemRect.width}px)`,"subsection"===this.mode&&(t.backgroundColor=this.activeColor),t},itemStyle(t){return t=>{const e={};return"subsection"===this.mode&&(e.borderColor=this.activeColor,e.borderWidth="1px",e.borderStyle="solid"),e}},textStyle(t){return t=>{const e={};return e.fontWeight=this.bold&&this.current===t?"bold":"normal",e.fontSize=uni.$u.addUnit(this.fontSize),"subsection"===this.mode?e.color=this.current===t?"#fff":this.inactiveColor:e.color=this.current===t?this.activeColor:this.inactiveColor,e}}},mounted(){this.init()},emits:["change"],methods:{init(){uni.$u.sleep().then((()=>this.getRect()))},getText(t){return"object"==typeof t?t[this.keyName]:t},getRect(){this.$uGetRect(".u-subsection__item--0").then((t=>{this.itemRect=t}))},clickHandler(t){this.$emit("change",t)}}},[["render",function(t,o,r,n,u,h){const g=l,p=f;return e(),s(g,{class:d(["u-subsection",[`u-subsection--${t.mode}`]]),ref:"u-subsection",style:a([t.$u.addStyle(t.customStyle),h.wrapperStyle])},{default:i((()=>[c(g,{class:d(["u-subsection__bar",["button"===t.mode&&"u-subsection--button__bar",0===t.current&&"subsection"===t.mode&&"u-subsection__bar--first",t.current>0&&t.current<t.list.length-1&&"subsection"===t.mode&&"u-subsection__bar--center",t.current===t.list.length-1&&"subsection"===t.mode&&"u-subsection__bar--last"]]),ref:"u-subsection__bar",style:a([h.barStyle])},null,8,["style","class"]),(e(!0),y(x,null,k(t.list,((o,l)=>(e(),s(g,{class:d(["u-subsection__item",[`u-subsection__item--${l}`,l<t.list.length-1&&"u-subsection__item--no-border-right",0===l&&"u-subsection__item--first",l===t.list.length-1&&"u-subsection__item--last"]]),ref_for:!0,ref:`u-subsection__item--${l}`,style:a([h.itemStyle(l)]),onClick:t=>h.clickHandler(l),key:l},{default:i((()=>[c(p,{class:"u-subsection__item__text",style:a([h.textStyle(l)])},{default:i((()=>[b(m(h.getText(o)),1)])),_:2},1032,["style"])])),_:2},1032,["class","style","onClick"])))),128))])),_:1},8,["class","style"])}],["__scopeId","data-v-0202131c"]]),W=I({__name:"comIndex",setup(t){const o=v(["组件","接口","模板"]);C((()=>{}));const a=v([{text:"文字转语音",url:"/pages/comExample/textToVoice/textToVoice"},{text:"组织树",url:"/pages/comExample/tree/tree"},{text:"socket连接",url:"/pages/comExample/socket/socket"},{text:"图片上传下载",url:"/pages/comExample/file-download/file-download"},{text:"手写签名",url:"/pages/comExample/signature/signature"},{text:"富文本编辑",url:"/pages/comExample/editRichText/editRichText"},{text:"防抖节流",url:"/pages/comExample/debounceAndThrottle/debounceAndThrottle"},{text:"ucharts示例",url:"/pages/comExample/uchartsDemo/uchartsDemo"},{text:"流程化工具bpmn示例",url:"/pages/comExample/bpmnDemo/bpmnDemo"},{text:"西瓜视频示例",url:"/pages/comExample/xgplay/xgplay"},{text:"图形解锁",url:"/pages/comExample/drawLock/drawLock"}]);const r=v(0),d=t=>{r.value=t};return(t,f)=>{n(u("uni-nav-bar"),R);const g=n(u("u-subsection"),E),p=l,_=n(u("u-icon"),T);return e(),s(p,{class:"comIndex"},{default:i((()=>[h("",!0),c(g,{list:o.value,mode:"button",style:{"margin-bottom":"30rpx"},fontSize:"16",current:r.value,onChange:d},null,8,["list","current"]),"0"==r.value?(e(!0),y(x,{key:1},k(a.value,((t,o)=>(e(),s(p,{class:"com-item",onClick:e=>{return s=t.url,void w({url:s});var s}},{default:i((()=>[c(p,{class:"item-l"},{default:i((()=>[b(m(t.text),1)])),_:2},1024),c(_,{name:"arrow-right",color:"#999",size:"18"})])),_:2},1032,["onClick"])))),256)):h("",!0),"1"==r.value?(e(),s(p,{key:2,class:""},{default:i((()=>[b(" 接口 ")])),_:1})):h("",!0),"2"==r.value?(e(),s(p,{key:3,class:""},{default:i((()=>[b(" 模板 ")])),_:1})):h("",!0)])),_:1})}}},[["__scopeId","data-v-79d52b41"]]);export{W as default};
