<template>
  <!-- 首页底部广告位 -->
  <view class="goodsSwiperBox" v-show="props.datas.list && props.datas.list.length > 0">
    <view class="gswTitle">
      <view class="gswTL" v-if="props.datas.title || props.datas.subLable">
        <text class="gswTL-text">{{ props.datas.title }}</text>
      </view>
    </view>
    <scroll-view
      scroll-x="true"
      v-if="props.datas.list && props.datas.list.length > 0"
      :display-multiple-items="props.config.multiple || 3"
      :autoplay="props.config.autoplay"
      :interval="props.config.interval || 5000"
      :duration="props.config.duration || 500"
      :circular="props.config.circular || false"
      :next-margin="props.config.hideMargin ? '0' : '100rpx'"
      :current="current"
      @change="swiperChange"
      :disable-touch="false"
    >
      <view class="banner" scroll-x="true">
        <view
          class="banner_Item"
          v-for="(item, index) in props.datas.list"
          :key="item.id"
          @tap="tapFun(item.jumpAddress, $event)"
          data-type="goods"
          :data-id="item.id"
          :data-index="index"
        >
          <block v-for="(t, i) in item.imgList">
            <image class="banner-img" :src="t.coverUrl" mode=""></image>
          </block>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { reactive, watch, ref } from 'vue'
import { config } from '@/config/config.js'

const props = defineProps(['datas', 'config'])
const emit = defineEmits(['change', 'tapFun'])

const current = ref(0)
function swiperChange(e) {
	current.value = e.detail.current
	emit('change', current.value)
}
function tapFun(i, e) {
	console.log(i,e);
  let url = i
  if (url === null) {
    // 默认跳转地址
    url = config.defalutOutLinkss
  }
  emit('tapFun', url)
}
</script>

<style lang="scss" scoped>
.goodsSwiperBox {
  width: 710rpx;
  height: 330rpx;
}
.goodsSwiperBox .gswTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  color: #999;
}
.goodsSwiperBox .gswTitle .gswTL {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.gswTL-text {
  margin: 22rpx 0 0 31rpx;
  font-size: 36rpx;
  color: #000000;
  font-weight: 700;
}
.banner {
  display: flex;
  margin: 15rpx 0 0 31rpx;
  height: 220rpx;
}
.banner_Item {
  display: flex;
  justify-content: space-between;
}
.banner-img {
  max-width: 300rpx;
  height: 100%;
  margin-right: 20rpx;
  image-rendering: -webkit-optomize-contrast;
  background-size: cover;
}
.uni-swiper-slides {
  right: unset;
}
</style>
