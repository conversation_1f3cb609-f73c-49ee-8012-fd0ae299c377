import{K as t,x as e,G as s,o as a,h as l,f as o,w as r,u as i,F as n,k as u,r as c,i as m,b as d,c as p,g as f,j as _,t as g,L as y,Q as j,B as b,S as w,p as v,l as k}from"./index-4d380fda.js";import{_ as x}from"./yuni-nav-bar.1e642613.js";import{_ as h}from"./u--image.daf5e625.js";import{_ as I}from"./uni-icons.3c47aaf1.js";import{_ as T}from"./yuni-tabbar.1db2d7be.js";import{m as C,n as F,o as U}from"./staticUrl.6af4fd3e.js";import{f as B,g as $}from"./request.a87c83d2.js";import{s as H}from"./crypto.39557b8f.js";import{_ as K}from"./_plugin-vue_export-helper.1b428a4d.js";import"./u-status-bar.0be47f49.js";import"./u-icon.66912310.js";import"./u-image.5419d1e1.js";import"./u-transition.dd4c905d.js";import"./u-safe-bottom.c99871d2.js";import"./uniUtils.7f6d2e72.js";import"./index.a555f9dd.js";import"./_commonjsHelpers.02d3be64.js";const L=K({__name:"work",setup(K){t();const L=e(C),N=e(F),P=s(),Q=e([]);!async function(){const t=await B({});Q.value=t.data.component}();const S=e(!0);return(t,e)=>{const s=u,C=c(d("yuni-nav-bar"),x),F=b,B=w,K=v,q=c(d("u--image"),h),z=k,G=c(d("uni-icons"),I),J=c(d("yuni-tabbar"),T);return a(),l(n,null,[o(C,{bgImageUrl:i(U),placeholder:"",fixed:"",title:"工作台",titleStyle:"color:#FFF"},{left:r((()=>[o(s)])),_:1},8,["bgImageUrl"]),o(s,{class:"work"},{default:r((()=>[(a(!0),l(n,null,m(Q.value,((t,e)=>(a(),p(s,{class:"work-box",key:e},{default:r((()=>["swipe"==t.component?(a(),p(s,{key:0,class:"yun-swiper"},{default:r((()=>[o(K,{class:"swiper-item","indicator-dots":!0,autoplay:!0,interval:3e3,duration:1e3,circular:"true"},{default:r((()=>[(a(!0),l(n,null,m(t.itemContent.data,((t,e)=>(a(),p(B,{key:e},{default:r((()=>[o(F,{class:"image",src:t.routerIcon,mode:"scaleToFill"},null,8,["src"])])),_:2},1024)))),128))])),_:2},1024)])),_:2},1024)):f("",!0),"notify"==t.component?(a(),p(s,{key:1,class:"yun-notify"},{default:r((()=>[o(s,{class:"left"},{default:r((()=>[o(q,{width:"30rpx",height:"30rpx",mode:"scaleToFill",src:L.value},null,8,["src"]),o(z,{class:"notify-title"},{default:r((()=>[_(g(t.itemContent.config.notifyText),1)])),_:2},1024)])),_:2},1024),o(G,{type:"forward",size:"22",color:"#6B6B6C"})])),_:2},1024)):f("",!0),"grid"==t.component?(a(),p(s,{key:2,class:"yun-grid"},{default:r((()=>[o(s,{class:"grid-title"},{default:r((()=>[o(q,{width:"6rpx",height:"30rpx",mode:"scaleToFill",src:N.value},null,8,["src"]),o(z,{class:"grid-title-text"},{default:r((()=>[_(g(t.itemContent.config.moduleTitle),1)])),_:2},1024)])),_:2},1024),o(s,{class:"grid-content"},{default:r((()=>[(a(!0),l(n,null,m(t.itemContent.data,((t,e)=>(a(),p(s,{class:"grid-item",key:e,onClick:e=>(t=>{let e=t.routerUrl,s=t.routerType,a=t.routerTitle;"localPage"==s?y({url:e,success(){S.value=!0}}):"netWeb"==s&&$({token:P.value}).then((t=>{let l=`${t.loginName}:${t.tenantId}`;const o="/pages/UNI/webview/webview?url="+e+"&type="+s+"&title="+a+"&mmy="+H(l);y({url:o,success(){S.value=!0}})})).catch((t=>{j({title:"请稍后重试",icon:"none"})}))})(t)},{default:r((()=>[o(F,{class:"image",src:t.routerIcon},null,8,["src"]),o(s,{class:"title"},{default:r((()=>[_(g(t.routerTitle),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:2},1024)])),_:2},1024)):f("",!0)])),_:2},1024)))),128)),o(J,{current:1})])),_:1})],64)}}},[["__scopeId","data-v-ff9dcdfd"]]);export{L as default};
