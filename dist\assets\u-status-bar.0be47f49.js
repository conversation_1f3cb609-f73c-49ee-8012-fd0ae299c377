import{d as s,m as t,a,o as e,c as o,w as r,e as u,n,k as i}from"./index-4d380fda.js";import{_ as d}from"./_plugin-vue_export-helper.1b428a4d.js";const l=d({name:"u-status-bar",mixins:[t,a,{props:{bgColor:{type:String,default:s.statusBar.bgColor}}}],data:()=>({}),computed:{style(){const s={};return s.height=uni.$u.addUnit(uni.$u.sys().statusBarHeight,"px"),s.backgroundColor=this.bgColor,uni.$u.deepMerge(s,uni.$u.addStyle(this.customStyle))}}},[["render",function(s,t,a,d,l,p){const c=i;return e(),o(c,{style:n([p.style]),class:"u-status-bar"},{default:r((()=>[u(s.$slots,"default",{},void 0,!0)])),_:3},8,["style"])}],["__scopeId","data-v-b69e9ea6"]]);export{l as _};
