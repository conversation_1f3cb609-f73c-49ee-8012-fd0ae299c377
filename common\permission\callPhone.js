// 拨打电话
// #ifdef APP-PLUS
import permission from '@/common/app-plus/permission'
import kvStore from '@/common/store/uniKVStore.js'
// #endif
export default async function callPhone(phone) {
	// #ifdef APP-PLUS
	if (uni.getSystemInfoSync().platform == "android") {
		let authPhone = kvStore.get('auth_callphone', true)
		console.log('本地电话权限状态', authPhone);
		if (authPhone) {
			let status = permission.isIOS ?
				await permission.requestIOS('call_phone') :
				await permission.requestAndroid('android.permission.CALL_PHONE')
			if (status == 1) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			} else if (status == 0) {
				return
			} else {
				permission.judgePermission("call_phone", (res) => {
					console.log("judgeResult=====call_phone" + JSON.stringify(res))
				});
			}
		} else {
			uni.showModal({
				title: '权限申请',
				content: '为了正常拨打电话，读取电话状态，短信验证服务，请允许使用拨打电话权限。你可以通过系统“设置”进行权限的管理',
				confirmText: '继续',
				cancelText: '关闭',
				success: async (r) => {
					if (r.confirm) {

						let status = permission.isIOS ?
							await permission.requestIOS('call_phone') :
							await permission.requestAndroid('android.permission.CALL_PHONE')
						if (status == 1) {
							kvStore.set('auth_callphone', true)
							uni.makePhoneCall({
								phoneNumber: phone
							})
						} else if (status == 0) {
							return
						} else {
							kvStore.set('auth_callphone', true)
							permission.judgePermission("call_phone", (res) => {
								console.log("judgeResult=====call_phone" + JSON.stringify(res))
							});
						}
					} else if (r.cancel) {
						return
					}
				}
			})
		}
	} else {
		uni.makePhoneCall({
			phoneNumber: phone
		})
	}
	// #endif
	// #ifndef APP-PLUS
	// #ifdef H5
	window.open(`tel:${phone}`)
	// #endif
	// #ifndef H5
	uni.makePhoneCall({
		phoneNumber: phone
	})
	// #endif
	// #endif

}