import{q as e,I as t,a6 as s,o as l,c as a,w as i,k as o,g as r,a0 as n,ap as u}from"./index-4d380fda.js";import{a as p}from"./uniUtils.7f6d2e72.js";const w={__name:"webview",setup(w){const y=e({url:"https://www.baidu.com",type:"web",webviewStyles:{progress:{color:"#FF3333"}},isNotSupported:!1,title:"智慧园区平台",mmy:""});function m(e){n({content:JSON.stringify(e.detail),showCancel:!1})}return t((e=>{e.mmy?y.url=e.url+"?mmy="+e.mmy:y.url=e.url,e.title&&(y.title=e.title),s({title:y.title}),p(e.type)&&("localPage"==e.type?y.type="local":"netWeb"==e.type&&(y.type="netweb"))})),(e,t)=>{const s=u,n=o;return l(),a(n,null,{default:i((()=>[y.isNotSupported?r("",!0):(l(),a(s,{key:0,"webview-styles":y.webviewStyles,src:y.url,onMessage:m},null,8,["webview-styles","src"]))])),_:1})}}};export{w as default};
