// import store from '@/store/index.js';	//vuex
import Platform from '@/common/platform/ePlatform.js'
import { isDef, isObj } from '@/common/uniUtils.js'
import { refreshToken } from '@/common/api.js'
import { isStrEmpty } from '@/common/uniUtils.js'
import { config } from '@/config/config.js' //appCode,clientId,BASEURL
import { useTokenStore } from '@/store/token.js'
import { useMainStore } from '@/store/index.js'
import kvStore from '@/common/store/uniKVStore'
// 新增修改
import * as dingtalk from 'dingtalk-jsapi'
import {
  dingLogin,
  getUserInfo,
} from '@/common/api.js'


let apiHeader = config.apiHeader
let apiHeader2 = config.apiHeader2
let server_url = config.BASEURL
let token = ''
let tokenType = ''
export let imageUrl = server_url + apiHeader + '/auth' + apiHeader2 + '/captcha'
var tokenStore
var mainStore
export async function request(options = {}) {
  try {
    const options_1 = await new Promise((resolved, rejected) => {
      tokenStore = useTokenStore()
      mainStore = useMainStore()
      if (options.url.search('http') == -1) {
        options.url = `${server_url}${options.url}`
      }
      if (
        isDef(options.params) &&
        isDef(options.params.uniContentType) &&
        options.params.uniContentType == 'json'
      ) {
        options.header = {
          clientId: config.clientId,
          'Content-Type': 'application/json;charset=UTF-8',
          scope: Platform,
          ...options.header,
        }
        delete options.params.uniContentType
      } else {
        options.header = {
          clientId: config.clientId,
          'Content-Type': 'application/x-www-form-urlencoded',
          scope: Platform,
          ...options.header,
        }
      }
      if (isDef(options.params)) {
        options.data = options.params
      }
      if (mainStore.hasLogin) {
        tokenStore.value && (token = tokenStore.value)
        tokenStore.tokenType && (tokenType = tokenStore.tokenType)
        options.header['Authorization'] = `${tokenType} ${token}`
      }
      if (!isStrEmpty(mainStore.jPushregisterId)) {
        options.header['jPushregisterId'] = mainStore.jPushregisterId
      }
      options.sslVerify = false
      resolved(options)
    })
    return new Promise((resolved_1, rejected_1) => {
      options_1.success = (res) => {
        if (
          isDef(res.data) &&
          isObj(res.data) &&
          res.data.hasOwnProperty('code')
        ) {
          let code = Number(res.data.code)
          switch (code) {
            case 1:
              resolved_1(res.data)
              break
            case 0:
              resolved_1(res.data)
              break
            case 402:
              rejected_1(res.data.message)
              break
            case 401: //todo 定义token失效的代码,刷新token
              if (options_1.url && options_1.url.endsWith('/auth/login')) {
                rejected_1(res.data.message)
                break
              }
              console.log('来这1', res)
            
              refreshToken(
                {
                  grant_type: 'refresh_token',
                  client_id: 'bVS46ElU',
                  client_secret: tokenStore.client_secret,
                  refresh_token: tokenStore.refreshToken.value,
                },
                options_1
              ).then((r) => {
                tokenStore.$patch((state) => {
                  state.refreshToken = r.data.refreshToken
                  state.tokenType = r.data.tokenType
                  state.value = r.data.value
                  state.expiration = r.data.expiration

                  kvStore.set('refreshTokenNum', 0)

                  if (options.url.search('/auth/oauth/check_token') != -1) {
                    options.data.token = r.data.value
                    options.params.token = r.data.value
                    let strNum = options.url.indexOf('?')
                    let str = options.url.substring(0, strNum)
                    options.url = str + '?token=' + r.data.value
                  }
                  let resData = request(options)
                  resolved_1(resData)
                })
              }).catch(()=>{
                mainStore.logout()
                tokenStore.clearAuthInfoToken()
                initConfig()
              })
              break
            default:
              rejected_1(res.data.message)
              break
          }
        } else if (
          isDef(res.data) &&
          isObj(res.data) &&
          res.hasOwnProperty('statusCode')
        ) {
          let code = Number(res.statusCode)
          switch (code) {
            case 200:
              resolved_1(res.data)
              break
            case 402:
              rejected_1(res.data.message)
              break
            case 400:
            case 401: //todo 定义token失效的代码,刷新token
              if (options_1.url && options_1.url.endsWith('/auth/login')) {
                rejected_1(res.data.message)
                break
              }
              refreshToken(
                {
                  grant_type: 'refresh_token',
                  client_id: 'bVS46ElU',
                  client_secret: tokenStore.client_secret,
                  refresh_token: tokenStore.refreshToken.value,
                },
                options_1
              ).then((r) => {
                tokenStore.$patch((state) => {
                  state.refreshToken = r.data.refreshToken
                  state.tokenType = r.data.tokenType
                  state.value = r.data.value
                  state.expiration = r.data.expiration

                  kvStore.set('refreshTokenNum', 0)

                  if (options.url.search('/auth/oauth/check_token') != -1) {
                    options.data.token = r.data.value
                    options.params.token = r.data.value
                    let strNum = options.url.indexOf('?')
                    let str = options.url.substring(0, strNum)
                    options.url = str + '?token=' + r.data.value
                  }
                  let resData = request(options)
                  resolved_1(resData)
                })
              })
              break
            default:
              rejected_1(res.data.message)
              break
          }
        } else if (!isDef(res.data)) {
          rejected_1('返回数据异常')
        } else if (isObj(res.data)) {
          //res.data.error = unauthorized 表示401
          if (res.data.error == 'unauthorized') {
            uni.reLaunch({
              url: '/pages/generalPage/noauthority/noauthority',
            })
          } else if (res.data.error == 'invalid_token') {

            console.log('来这3', res)
            if (options_1.url && options_1.url.endsWith('/auth/login')) {
              rejected_1(res.data.message)
              return
            }
            refreshToken(
              {
                grant_type: 'refresh_token',
                client_id: 'bVS46ElU',
                client_secret: tokenStore.client_secret,
                refresh_token: tokenStore.refreshToken.value,
              },
              options_1
            ).then((r) => {
              tokenStore.$patch((state) => {
                state.refreshToken = r.data.refreshToken
                state.tokenType = r.data.tokenType
                state.value = r.data.value
                state.expiration = r.data.expiration

                kvStore.set('refreshTokenNum', 0)

                if (options.url.search('/auth/oauth/check_token') != -1) {
                  options.data.token = r.data.value
                  options.params.token = r.data.value
                  let strNum = options.url.indexOf('?')
                  let str = options.url.substring(0, strNum)
                  options.url = str + '?token=' + r.data.value
                }
                let resData = request(options)
                resolved_1(resData)
              })
            })
          }
          // resolved_1(res.data)
        }
      }
      options_1.fail = (err) => {
        console.log('请求失败', err)
        rejected_1(err)
      }
      uni.request(options_1)
    })
  } catch (err_1) {
    return Promise.reject(err_1)
  }
}

export async function refresh(options = {}) {
  let num = Number(kvStore.get('refreshTokenNum', true))
  console.log('次数', num)
  if (num >= 3) {
    mainStore.logout()
    tokenStore.clearAuthInfoToken()
    initConfig()



    return
  } else {
    num = num + 1
    kvStore.set('refreshTokenNum', num)
  }
  try {
    const options_1 = await new Promise((resolved, rejected) => {
      tokenStore = useTokenStore()
      mainStore = useMainStore()
      options.url = `${server_url}${options.url}`
      options.data = options.params
      options.header = {
        clientId: config.clientId,
        'Content-Type': 'application/x-www-form-urlencoded',
        scope: Platform,
      }
      if (mainStore.hasLogin) {
        tokenStore.value && (token = tokenStore.value)
        tokenStore.tokenType && (tokenType = tokenStore.tokenType)
        options.header['Authorization'] = `${tokenType} ${token}`
      }
      resolved(options)
    })
    return new Promise((resolved_1, rejected_1) => {
      options_1.success = (res) => {
        if (
          isDef(res.data) &&
          isObj(res.data) &&
          res.data.hasOwnProperty('code')
        ) {
          let code = Number(res.data.code)
          switch (code) {
            case 1:
              resolved_1(res.data)
              break
            case 0:
              resolved_1(res.data)
              break
            case 402:
              rejected_1(res.data.message)
              break
            case 401: // 定义token失效的代码,刷新token
              refreshToken(
                {
                  grant_type: 'refresh_token',
                  client_id: 'bVS46ElU',
                  client_secret: tokenStore.client_secret,
                  refresh_token: tokenStore.refreshToken.value,
                },
                options_1
              ).then((r) => {
                tokenStore.$patch((state) => {
                  state.refreshToken = r.data.refreshToken
                  state.tokenType = r.data.tokenType
                  state.value = r.data.value
                  state.expiration = r.data.expiration
                })
              })
              break
            default:
              rejected_1(res.data.message)
              break
          }
        } else if (!isDef(res.data)) {
          rejected_1('返回数据异常')
        } else if (isObj(res.data)) {
          //res.data.error = unauthorized 表示401
          if (res.data.error == 'unauthorized') {
            uni.reLaunch({
              url: '/pages/generalPage/noauthority/noauthority',
            })
          }
          if (res.data.error == 'invalid_token') {
            kvStore.set('refreshTokenNum', 0)
            uni.showToast({
              title: '登录信息已失效，请重新登录',
              icon: 'none',
              success: () => {
                mainStore.logout()
                tokenStore.clearAuthInfo()
                setTimeout(() => {
                  uni.reLaunch({
                    url: '/pages/login/login',
                    success: () => { }
                  })
                }, 2000)
              },
            })
          }
          resolved_1(res.data)
        }
      }
      options_1.fail = (err) => {
        console.log('请求失败', err)
        rejected_1(err)
      }
      uni.request(options_1)
    })
  } catch (err_1) {
    return Promise.reject(err_1)
  }
}

export async function uploadFile(options = {}) {
  try {
    const options_1 = await new Promise((resolved, rejected) => {
      tokenStore = useTokenStore()
      mainStore = useMainStore()
      options.url = `${server_url}${options.url}`
      if (isDef(options.params)) {
        options.filePath = options.params.filePath
        options.name = 'multipartFile'
        options.formData = {
          appCode: config.appCode,
        }
      }
      options.header = {
        clientId: config.clientId,
        scope: Platform,
      }
      if (mainStore.hasLogin) {
        tokenStore.value && (token = tokenStore.value)
        tokenStore.tokenType && (tokenType = tokenStore.tokenType)
        options.header['Authorization'] = `${tokenType} ${token}`
      }
      resolved(options)
    })
    return new Promise((resolved_1, rejected_1) => {
      options_1.success = (res) => {
        let result = JSON.parse(res.data)
        if (isDef(result) && isObj(result) && result.hasOwnProperty('code')) {
          let code = Number(result.code)
          switch (code) {
            case 1:
              resolved_1(result)
              break
            case 0:
              resolved_1(result)
              break
            case 402:
              rejected_1(result.message)
              break
            case 401: // 定义token失效的代码,刷新token
              if (options_1.url && options_1.url.endsWith('/auth/login')) {
                rejected_1(result.message)
                break
              }
              refreshToken(
                {
                  grant_type: 'refresh_token',
                  client_id: 'bVS46ElU',
                  client_secret: tokenStore.client_secret,
                  refresh_token: tokenStore.refreshToken.value,
                },
                options_1
              ).then((r) => {
                tokenStore.$patch((state) => {
                  state.refreshToken = r.data.refreshToken
                  state.tokenType = r.data.tokenType
                  state.value = r.data.value
                  state.expiration = r.data.expiration
                })
              })
            default:
              rejected_1(result.message)
              break
          }
        } else if (!isDef(result)) {
          rejected_1('返回数据异常')
        } else if (isObj(result)) {
          //res.data.error = unauthorized 表示401
          if (result.error == 'unauthorized') {
            uni.reLaunch({
              url: '/pages/generalPage/noauthority/noauthority',
            })
          }
          resolved_1(result)
        }
      }
      options_1.fail = (err) => {
        console.log('请求失败', err)
        rejected_1(err)
      }
      uni.uploadFile(options_1)
    })
  } catch (err_1) {
    return Promise.reject(err_1)
  }
}

export async function downloadFile(options = {}) {
  try {
    const options_1 = await new Promise((resolved, rejected) => {
      tokenStore = useTokenStore()
      mainStore = useMainStore()
      options.url = `${server_url}${options.url}`
      options.header = {
        clientId: config.clientId,
        scope: Platform,
      }
      if (mainStore.hasLogin) {
        tokenStore.value && (token = tokenStore.value)
        tokenStore.tokenType && (tokenType = tokenStore.tokenType)
        options.header['Authorization'] = `${tokenType} ${token}`
      }
      resolved(options)
    })
    return new Promise((resolved_1, rejected_1) => {
      options_1.success = (res) => {
        if (isDef(res) && isObj(res)) {
          resolved_1(res)
        } else if (!isDef(res)) {
          rejected_1('返回数据异常')
        }
      }
      options_1.fail = (err) => {
        console.log('请求失败', err)
        rejected_1(err)
      }
      uni.downloadFile(options_1)
    })
  } catch (err_1) {
    return Promise.reject(err_1)
  }
}


let pageName = ''
let visiterObj = {}

// 初始化配置
function initConfig() {
  pageName = getQueryVariable().pageName;
  getCode()
  // 其他初始化逻辑
}

function getQueryVariable() {
  const geturl = localStorage.getItem('urljumpInfo') || ''

  setTimeout(() => {
    localStorage.removeItem('urljumpInfo')
  }, 2000)


  let getqyinfo = geturl.split("?")[1];
  if (!getqyinfo) {
    return false;
  }

  let getqys = getqyinfo.split("&");
  let obj = {}; //创建空对象，接收截取的参数
  for (let i = 0; i < getqys.length; i++) {
    let item = getqys[i].split("=");
    let key = item[0];
    let value = item[1];
    obj[key] = value;
  }

  
  return obj;
}
// 通过钉钉获取code
function getCode() {
  if (dingtalk.env.platform !== "notInDingTalk") {
    uni.showLoading({
      title: "授权登录中...",
    })
    dingtalk.ready(() => {
      dingtalk.runtime.permission.requestAuthCode({
        corpId: config.corpId,
        onSuccess: (res) => {
          const { code } = res
          handleDingTalkLogin(code)
        },
        onFail: (err) => {
          uni.hideLoading()
          uni.showToast({
            title: "授权失败",
            icon: "none",
            mask: true,
          })
          uni.reLaunch({ url: "/pages/login/login" })
        },
      })
    })


  } else {
    uni.reLaunch({ url: "/pages/login/login" })
  }
}
// 处理页面跳转
function handlePageJump() {

  if (pageName) {
    if (pageName.indexOf("carPlace") != -1) {
      const pageNames = pageName.split("-")
      visiterObj.id = pageNames[1]
      uni.reLaunch({ url: "/pages/vehicle-management/parking-record/detail?id=" + visiterObj.id });
      return
    }

    if (pageName.indexOf("carOrder") != -1) {
      const pageNames = pageName.split("-")
      visiterObj.id = pageNames[1]
      uni.reLaunch({ url: "/pages/vehicle-management/parking-order/detail?id=" + visiterObj.id });
      return
    }
  }

  uni.reLaunch({ url: "/pages/home/<USER>" });
}


// 钉钉登录
async function handleDingTalkLogin(code) {


  try {
    const res = await dingLogin({ appLabel: config.appLabel, corpId: config.corpId, code: code, type: 'enterprise_internal' })
    // uni.showModal({
    //   title: '提示',
    //   content: JSON.stringify(res.data),
    //   showCancel: false
    // })
    uni.hideLoading()
    keepUserInfo(res.data)
   
  } catch (error) {
    uni.showToast({
      title: "授权失败",
      icon: "none",
      mask: true,
    })
    uni.reLaunch({ url: "/pages/login/login" })
    uni.hideLoading()
  }


}

function handleLoginSuccess() {
  handlePageJump()
}

//保存用户信息
function keepUserInfo(result) {
  // 更新个人信息到vuex
  tokenStore.$patch((state) => {
    state.refreshToken = result.refreshToken
    state.tokenType = result.tokenType
    state.value = result.value
    state.expiration = result.expiration
    state.userid = result.additionalInformation.userid
    state.tenantId = result.additionalInformation.customParam.tenantId
  })
  mainStore.login(Platform)
  // 携带token 调取接口获取个人
  getUserInfo({
    token: result.value,
  })
    .then((res1) => {
      // console.log('个人信息',res1)
      // 保存个人信息
      userStore.userInfo = res1
      handleLoginSuccess()
    })
    .catch((err) => {
      handleLoginSuccess()
    })
}
function jump2Login() {
  uni.reLaunch({
    url: '/pages/login/login',
  })
}
// 登录成功后，处理函数