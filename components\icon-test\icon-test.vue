<template>
  <view class="icon-test-container">
    <view class="test-section">
      <text class="section-title">Iconfont 测试</text>
      <view class="icon-grid">
        <view class="icon-item">
          <text class="iconfont icon-arrow-right"></text>
          <text class="icon-label">arrow-right</text>
        </view>
        <view class="icon-item">
          <text class="iconfont icon-calendar"></text>
          <text class="icon-label">calendar</text>
        </view>
        <view class="icon-item">
          <text class="iconfont icon-comment"></text>
          <text class="icon-label">comment</text>
        </view>
        <view class="icon-item">
          <text class="iconfont icon-file"></text>
          <text class="icon-label">file</text>
        </view>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">Iconfonts 测试</text>
      <view class="icon-grid">
        <view class="icon-item">
          <text class="iconfonts icon-tingchejilu2"></text>
          <text class="icon-label">tingchejilu2</text>
        </view>
        <view class="icon-item">
          <text class="iconfonts icon-wodecheliang"></text>
          <text class="icon-label">wodecheliang</text>
        </view>
        <view class="icon-item">
          <text class="iconfonts icon-shouye"></text>
          <text class="icon-label">shouye</text>
        </view>
        <view class="icon-item">
          <text class="iconfonts icon-qian"></text>
          <text class="icon-label">qian</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('Icon test component mounted')
})
</script>

<style lang="scss" scoped>
.icon-test-container {
  padding: 20rpx;
}

.test-section {
  margin-bottom: 40rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  color: #333;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.icon-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  background: #fafafa;
}

.icon-item text:first-child {
  font-size: 40rpx;
  color: #c20000;
  margin-bottom: 8rpx;
  line-height: 1;
}

.icon-label {
  font-size: 20rpx;
  color: #666;
  text-align: center;
  word-break: break-all;
  line-height: 1.2;
}

/* 确保图标字体正确加载 */
.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
