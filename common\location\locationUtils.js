import { LOGO_URL, LOCATION_URL } from '@/common/net/staticUrl.js'
import { config } from '@/config/config.js'
import QQMapWX from '@/components/tx-map/lib/qqmap-wx-jssdk.min.js'

const tMap = new QQMapWX({
  key: config.QQMapWXKey,
})

export function getLocation(successCb, authDenyCb) {
  let data = {
    isLocated: false,
    reportInfo: {},
    //小区
    plot: {},
    covers: [],
    distance: 0.1,
    //marker
    markers: [
      {
        id: 1,
        latitude: Number(36.677848),
        longitude: Number(117.134524),
        width: 20,
        height: 20,
        iconPath: LOCATION_URL,
        title: '标记地点1',
      },
      {
        id: 2,
        latitude: Number(36.685808),
        longitude: Number(117.128252),
        width: 20,
        height: 20,
        iconPath: LOCATION_URL,
        title: '标记地点2',
      },
    ],
    //目标地址
    destination: {
      name: '济南东站',
      latitude: 36.747806,
      longitude: 117.159864,
    },
  }
  uni.getLocation({
    type: 'gcj02', // 'wgs84'默认gps 坐标 'gcj02'国测
    altitude: false, // 是否返回高度
    accuracy: 'best', // 精度值为20m
    geocode: true,
    success(res) {
      console.log(res)
      successCb && successCb(res)
      data.isLocated = true
      //获取经纬度
      data.reportInfo.latitude = res.latitude
      data.reportInfo.longitude = res.longitude
      //我的位置
      var obj = {
        id: 0,
        width: 30,
        height: 30,
        latitude: Number(data.reportInfo.latitude),
        longitude: Number(data.reportInfo.longitude),
        iconPath: LOCATION_URL,
      }

      data.distance = getMapDistance(
        data.reportInfo.latitude,
        data.reportInfo.longitude,
        data.destination.latitude,
        data.destination.longitude
      )
      var arr = []
      arr.push(obj)
      // arr.push(bin);
      data.markers.forEach((item, index) => {
        //js遍历数组
        var bin = {
          id: index + 2,
          latitude: Number(item.latitude),
          longitude: Number(item.longitude),
          width: 20,
          height: 20,
          iconPath: item.iconPath,
          title: item.title,
        }
        arr.push(bin) //push() 方法可向数组的末尾添加一个或多个元素，并返回新的长度。
      })

      //标记点
      data.covers = arr
    },
    fail(err) {
      if (
        err.errMsg ===
        'getLocation:fail 频繁调用会增加电量损耗，可考虑使用 wx.onLocationChange 监听地理位置变化'
      ) {
        uni.showToast({
          title: '请勿频繁定位',
          icon: 'none',
        })
      }
      if (err.errMsg === 'getLocation:fail auth deny') {
        // 未授权
        uni.showToast({
          title: '无法定位，请重新获取位置信息',
          icon: 'none',
        })
        authDenyCb && authDenyCb()
        data.isLocated = false
      }
      if (
        err.errMsg === 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF'
      ) {
        uni.showModal({
          content: '请开启手机定位服务',
          showCancel: false,
        })
      }
    },
    complete() {
      console.log(data.reportInfo)
      // #ifdef MP-WEIXIN
      tMap.reverseGeocoder({
        location: {
          latitude: data.reportInfo.latitude,
          longitude: data.reportInfo.longitude,
        },
        success: function (res) {
          console.log('解析地址成功')
          console.log(res)
          data.plot = res.result.formatted_addresses
          data.address = res.result.address
          data.addressComponent = res.result.address_component
          // 省
          // let province = res.result.ad_info.province;
          // 市
          let city = res.result.ad_info.city
          // console.log(province);
        },
        fail: function (res) {
          uni.showToast({
            title: '定位失败',
            duration: 2000,
            icon: 'none',
          })
          console.log(res)
        },
        complete: function (res) {
          //无论成功失败都会执行
          console.log(res)
        },
      })
      // #endif
    },
  })
  console.log('data:', data)
  return data
}

/*
	 计算距离，参数分别为第一点的纬度，经度；第二点的纬度，经度
	 默认单位km
	*/
export function getMapDistance(lat1, lng1, lat2, lng2) {
  console.log('计算距离')
  var radLat1 = Rad(lat1)
  var radLat2 = Rad(lat2)
  var a = radLat1 - radLat2
  var b = Rad(lng1) - Rad(lng2)
  var s =
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)
      )
    )
  s = s * 6378.137 // EARTH_RADIUS;
  s = Math.round(s * 10000) / 10000 //输出为公里
  //s=s.toFixed(2);
  return s
}

//进行经纬度转换为距离的计算
export function Rad(d) {
  return (d * Math.PI) / 180.0 //经纬度转换成三角函数中度分表形式。
}
