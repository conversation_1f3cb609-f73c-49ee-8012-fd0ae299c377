<template>
    <!-- #ifdef H5-DINGTALK -->
    <uni-nav-bar
      :title="navTitle"
      :left-icon="leftIcon"
      v-if="showNavBar"
      fixed
      status-bar
      @clickLeft="handleLeftClick"
    >
      <!-- <template v-if="$slots.left" #left>
        <slot name="left" />
      </template> -->
    </uni-nav-bar>
    <!-- #endif -->
  </template>
  
  <script setup>
  import { ref, watchEffect, onMounted } from 'vue'
  import * as myDIng from 'dingtalk-jsapi'
  
  const props = defineProps({
    title: {
      type: String,
      default: '页面标题'
    },
    showBack: {
      type: Boolean,
      default: true
    },
    leftIcon: {
      type: String,
      default: 'left'
    },
    hideNavBar: {
      type: Boolean,
      default: false
    },
    isBackHome: {
      type: Boolean,
      default: false
    }
  })
  
  const emit = defineEmits(['back'])
  
  const navTitle = ref(props.title)
  const showNavBar = ref(!props.hideNavBar)
  
  // 处理钉钉环境导航栏设置
  const setupDingNavigation = () => {
    try {
      if (props.hideNavBar) {
        myDIng.biz.navigation.hideBar({ hidden: true })
      } else {
        myDIng.biz.navigation.setTitle({ title: props.title })
      }
    } catch (e) {
      console.error('钉钉API调用失败:', e)
    }
  }
  
  // 返回按钮点击处理
  const handleLeftClick = () => {
    if (props.showBack) {
      // emit('back')
      uni.navigateBack({ delta: 1 })
    }
  }
  
  // 环境检测（可选）
  const isDingTalk = ref(false)
  onMounted(() => {
    // #ifdef H5
    isDingTalk.value = navigator.userAgent.includes('DingTalk')
    // #endif
  })
  
  // 监听标题变化
  watchEffect(() => {
    navTitle.value = props.title
    if (!props.hideNavBar && isDingTalk.value) {
      myDIng.biz.navigation.setTitle({ title: props.title })
    }
  })
  
  // 初始化设置
  onMounted(() => {
    // #ifdef H5-DINGTALK
    setupDingNavigation()
    // #endif
  })
  </script>
  
  <style lang="scss">
  // 可添加自定义样式或留空由父组件控制
  // .uni-nav-bar {
  //   .uni-nav-bar__header {
  //     background-color: #ffffff !important;
  //   }
    
  //   .uni-nav-bar__text {
  //     color: #333 !important;
  //   }
  // }
  </style>