import {  request } from '@/common/net/request.js'

import { config } from '@/config/config.js'
let apiUrl = config.apiUrl
let apiHeader = config.apiHeader
let apiHeader2 = config.apiHeader2
// 账户列表
export function pageList(params) {
  params['uniContentType'] = 'json'
    return request({
        url: `${apiHeader}/pay${apiUrl}${apiHeader2}/pay/payAccountList/pageList`,
        method: 'post',
        params
      })
    
}

// 个人账户信息查询：
export function accountInfo(params) {
  params['uniContentType'] = 'json'
    return request({
        url: `${apiHeader}/pay${apiUrl}${apiHeader2}/pay/payAccountInfo/app/list`,
        method: 'post',
        params
      })

}
// 修改账户自定义排序：
export function updateSort(params) {
  params['uniContentType'] = 'json'
    return request({
        url: `${apiHeader}/pay${apiUrl}${apiHeader2}/pay/payAccountInfo/app/updateSort`,
        method: 'post',
        params
      })
}

// 个人账户统计查询
export function statistic(params) {
  params['uniContentType'] = 'json'
    return request({
        url: `${apiHeader}/pay${apiUrl}${apiHeader2}/pay/payAccountInfo/app/statistic`,
        method: 'post',
        params
      })
}

// 账户详情：
export function accountDetail(params) {
  params['uniContentType'] = 'json'
    return request({
        url: `${apiHeader}/pay${apiUrl}${apiHeader2}/pay/PayAccountManage/detail`,
        method: 'post',
        params
      })
}

// 账户详情：
export function accountGetInfo(params) {
  params['uniContentType'] = 'json'
    return request({
        url: `${apiHeader}/pay${apiUrl}${apiHeader2}/pay/payAccountDetails/app/getInfo`,
        method: 'post',
        params
      })
}