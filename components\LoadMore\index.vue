<template>
    <view class="load-more-container">
      <!-- 加载状态 -->
      <view  class="loading-content">
        <view class="blinking-dots">
          <view class="dot dot1"></view>
          <view class="dot dot2"></view>
          <view class="dot dot3"></view>
        </view>
        <text class="loading-text">加载中</text>
      </view>
  
    </view>
  </template>
  
  <script setup>
  // 保持原有逻辑不变
  import { ref, onMounted, onUnmounted } from 'vue'
  
  const props = defineProps({
    loading: Boolean,
    noMore: Boolean
  })
  
  const emit = defineEmits(['loadmore'])
  
  let observer = null
  const observerRef = ref(null)
  
  const initObserver = () => {
    observer = uni.createIntersectionObserver(this)
    observer.relativeToViewport()
      .observe('.observer', (res) => {
        if (res.intersectionRatio > 0 && !props.loading && !props.noMore) {
          emit('loadmore')
        }
      })
  }
  
  onMounted(initObserver)
  onUnmounted(() => observer?.disconnect())
  </script>
  
  <style scoped>
  /* 保持容器样式不变 */
  .load-more-container {
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .loading-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  /* 新增闪烁动画样式 */
  .blinking-dots {
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #666;
    opacity: 0.3;
    animation: blink 1.4s infinite both;
  }
  
  .dot1 { animation-delay: 0.2s; }
  .dot2 { animation-delay: 0.4s; }
  .dot3 { animation-delay: 0.6s; }
  
  @keyframes blink {
    0%, 80%, 100% { 
      opacity: 0.3;
      transform: scale(0.8);
    }
    40% {
      opacity: 1;
      transform: scale(1.2);
      background-color: #1890ff; /* 高亮颜色 */
    }
  }
  
  /* 原有其他样式保持不变 */
  .loading-text, .no-more-text {
    font-size: 12px;
    color: #999;
  }
  
  .observer {
    height: 1px;
    opacity: 0;
  }
  
  @media (min-width: 768px) {
    .load-more-container {
      padding: 24px 0;
    }
    
    .loading-text, .no-more-text {
      font-size: 14px;
    }
    
    .dot {
      width: 10px;
      height: 10px;
    }
  }
  </style>