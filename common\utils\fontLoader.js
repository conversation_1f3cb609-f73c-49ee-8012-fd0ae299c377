/**
 * 字体加载工具
 * 用于动态加载和检查字体文件
 */

class FontLoader {
  constructor() {
    this.loadedFonts = new Set()
    this.fontPromises = new Map()
  }

  /**
   * 加载字体文件
   * @param {string} fontFamily 字体族名称
   * @param {string} fontUrl 字体文件URL
   * @returns {Promise<boolean>} 加载是否成功
   */
  async loadFont(fontFamily, fontUrl) {
    // 如果已经加载过，直接返回成功
    if (this.loadedFonts.has(fontFamily)) {
      return true
    }

    // 如果正在加载中，返回现有的Promise
    if (this.fontPromises.has(fontFamily)) {
      return this.fontPromises.get(fontFamily)
    }

    // 创建加载Promise
    const loadPromise = this._loadFontFile(fontFamily, fontUrl)
    this.fontPromises.set(fontFamily, loadPromise)

    try {
      const result = await loadPromise
      if (result) {
        this.loadedFonts.add(fontFamily)
      }
      return result
    } catch (error) {
      console.error(`字体加载失败: ${fontFamily}`, error)
      return false
    } finally {
      this.fontPromises.delete(fontFamily)
    }
  }

  /**
   * 实际加载字体文件
   * @private
   */
  async _loadFontFile(fontFamily, fontUrl) {
    // #ifdef H5
    if (typeof document !== 'undefined' && 'fonts' in document) {
      try {
        // 使用FontFace API加载字体
        const fontFace = new FontFace(fontFamily, `url(${fontUrl})`)
        await fontFace.load()
        document.fonts.add(fontFace)
        console.log(`字体加载成功: ${fontFamily}`)
        return true
      } catch (error) {
        console.error(`FontFace API加载失败: ${fontFamily}`, error)
        // 降级到CSS方式
        return this._loadFontWithCSS(fontFamily, fontUrl)
      }
    } else {
      return this._loadFontWithCSS(fontFamily, fontUrl)
    }
    // #endif

    // #ifndef H5
    // 在非H5环境下，假设字体文件已经通过CSS正确加载
    console.log(`非H5环境，假设字体已加载: ${fontFamily}`)
    return true
    // #endif
  }

  /**
   * 使用CSS方式加载字体
   * @private
   */
  _loadFontWithCSS(fontFamily, fontUrl) {
    // #ifdef H5
    if (typeof document !== 'undefined') {
      try {
        // 创建style元素
        const style = document.createElement('style')
        style.textContent = `
          @font-face {
            font-family: '${fontFamily}';
            font-weight: normal;
            font-style: normal;
            src: url('${fontUrl}') format('truetype');
          }
        `
        document.head.appendChild(style)

        // 创建测试元素来触发字体加载
        const testElement = document.createElement('span')
        testElement.style.fontFamily = fontFamily
        testElement.style.fontSize = '1px'
        testElement.style.opacity = '0'
        testElement.style.position = 'absolute'
        testElement.style.left = '-9999px'
        testElement.textContent = 'test'
        document.body.appendChild(testElement)

        // 等待字体加载
        return new Promise((resolve) => {
          const checkFont = () => {
            const computedStyle = window.getComputedStyle(testElement)
            if (computedStyle.fontFamily.includes(fontFamily)) {
              document.body.removeChild(testElement)
              console.log(`CSS方式字体加载成功: ${fontFamily}`)
              resolve(true)
            } else {
              setTimeout(checkFont, 100)
            }
          }
          
          // 开始检查，最多等待3秒
          setTimeout(() => {
            if (document.body.contains(testElement)) {
              document.body.removeChild(testElement)
            }
            console.log(`CSS方式字体加载超时: ${fontFamily}`)
            resolve(false)
          }, 3000)
          
          checkFont()
        })
      } catch (error) {
        console.error(`CSS方式加载失败: ${fontFamily}`, error)
        return false
      }
    }
    // #endif
    
    return false
  }

  /**
   * 检查字体是否已加载
   * @param {string} fontFamily 字体族名称
   * @returns {boolean} 是否已加载
   */
  isFontLoaded(fontFamily) {
    return this.loadedFonts.has(fontFamily)
  }

  /**
   * 预加载项目中的所有图标字体
   * @returns {Promise<Object>} 加载结果
   */
  async preloadIconFonts() {
    const results = {}
    
    try {
      // 加载iconfont
      results.iconfont = await this.loadFont('iconfont', '/static/iconfont/iconfont.ttf')
      
      // 加载iconfonts
      results.iconfonts = await this.loadFont('iconfonts', '/static/iconfonts/iconfont.ttf')
      
      console.log('图标字体预加载结果:', results)
      return results
    } catch (error) {
      console.error('图标字体预加载失败:', error)
      return { iconfont: false, iconfonts: false }
    }
  }
}

// 创建全局实例
const fontLoader = new FontLoader()

export default fontLoader
export { FontLoader }
