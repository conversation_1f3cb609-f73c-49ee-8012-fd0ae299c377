import {
	systemUpdate
} from '@/common/api'
import {
	config
} from '@/config/config.js'
import {
	version,
	name,
	appPackage,
	appIdentify
} from '@/package.json'
const platform = uni.getSystemInfoSync().platform
export default {
	// 发起ajax请求获取服务端版本号
	getServerNo: (version, tenantId, softType, isPrompt = false, callback) => {
		/* 接口入参说明
		 * softName: 应用名称（必填）
		 * softVersion：应用版本名称（必填）
		 * softType：‘app’-移动app，‘patch’-补丁（必填）
		 * tenantId: 租户id （必填）
		 * packName：包名（必填）
		 * deviceNet: 网络类型（非必填）
		 * deviceType: 平台（android/ios等等）
		 * deviceId：设备唯一标识
		 * deviceFirm：设备的生产厂商
		 * deviceModel：设备的型号
		 * deviceIdType: 设备身份码，因需要用户授权，不再要求（非必填）
		 */
		let deviceType = uni.getSystemInfoSync().osName
		let deviceId = plus.device.uuid ? plus.device.uuid : ''
		let deviceFirm = plus.device.vendor
		let deviceModel = plus.device.model
		let deviceNet = ''
		let httpParams = {
			softName: config.softName,
			softVersion: version.appVersionName,
			softType: softType,
			tenantId: tenantId,
			packName: appPackage,
			deviceType: deviceType,
			deviceId: deviceId,
			deviceIdType: '',
			deviceFirm: deviceFirm,
			deviceModel: deviceModel
		}
		uni.getNetworkType({
			success: function(res) {
				httpParams.deviceNet = res.networkType
			},
			complete: function() {
				// APP更新接口
				systemUpdate(httpParams).then((res) => {
					if (res && res.data.code != 'nothing') {
						/* updateType值
		  		force: 强制更新
		  		ask: 弹窗确认更新
		  		silence: 静默更新 
		  	*/
						console.log('callback', res.data.data)
						callback && callback(res.data.data)
					} else {
						if (isPrompt) {
							uni.showToast({
								title: '您使用的已是最新版本',
								icon: 'none',
							})
						}
					}
				})
			}
		})
	},
	// 弹窗主颜色（不填默认粉色）
	appUpdateColor: 'f00',
	// 弹窗图标（不填显示默认图标，链接配置示例如： '/static/demo/ic_attention.png'）
	appUpdateIcon: '',
}