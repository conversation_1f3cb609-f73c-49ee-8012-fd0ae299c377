<template>
	<view :class="{'my-tabs': true, 'space-between': formatBe}">
	  <view
		v-for="(item, index) in getModelData"
		:key="index"
		:class="{'tab-item': true, 'active': formatIndex === index}"
		@tap="tap(index)"
	  >
		{{ item.label }}
	  </view>
	</view>
  </template>

  <script setup>
import { ref, computed } from 'vue'

// 定义 props
const props = defineProps({
  modelData: {
    type: Array,
    default: () => []
  },
  initIndex: {
    type: Number,
    default: 0
  }
})

// 定义 emit
const emit = defineEmits(['change'])

// 计算属性：获取 modelData
const getModelData = computed(() => props.modelData)

// 计算属性：判断是否使用 space-between
const formatBe = computed(() => {
  return props.modelData && props.modelData.length > 4
})

// 计算属性：获取当前选中的索引
const formatIndex = computed(() => props.initIndex)

// 点击事件
const tap = (index) => {
  console.log("我点击了", index)
  emit('change', index)
}
</script>

<style lang="scss">
.my-tabs {
  background-color: #ffffff;
  height: 88upx;
  font-size: 28upx;
  display: flex;
  justify-content: space-around;
  box-sizing: border-box;
  border-top: 2upx solid #dddddd;
  border-bottom: 2upx solid #dddddd;
  min-width: 100%;
  overflow-x: auto;

  .tab-item {
    line-height: 48upx;
    padding: 20upx;
    min-width: 100upx;
    text-align: center;
   white-space:nowrap;
  }

  .tab-item.active {
    position: relative;
    color: #3682FF;
  }

  .tab-item.active::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    border-bottom: 4upx solid #3682FF;
    animation: test ease 1 1.5s;
  }
}

.my-tabs.space-between {
  justify-content: space-between;
}

@keyframes test {
  0% {
    width: 100%;
  }
  50% {
    width: 150%;
  }
  100% {
    width: 100%;
  }
}
</style>