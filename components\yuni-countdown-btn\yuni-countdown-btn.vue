<template>
  <view>
    <u-button
      v-if="status"
      text="获取验证码"
      type="primary"
      size="mini"
      :customStyle="data.customBtnStyle"
      shape="circle"
	    color="linear-gradient(150deg,#c20000 0%,#c20000 100%)"
      @click="getCaptha"
    ></u-button>
    <u-button
      v-if="!status"
      :disabled="true"
      :text="time + 's'"
      type="primary"
      size="mini"
      :customStyle="data.customBtnStyle"
	    color="linear-gradient(150deg,#c20000 0%,#c20000 100%)"
      shape="circle"
    ></u-button>
  </view>
</template>
<script setup>
import { reactive, ref, watch } from 'vue'
import { getVerificationCode } from '@/common/api.js'
const props = defineProps(['countTime', 'cellPhone', 'url', 'account'])
const data = reactive({
  customBtnStyle: {
    height: '60rpx',
    width: '160rpx',
  },
})
var time = ref(props.countTime)
var status = ref(true)
var timeInterval
function getCaptha() {
  var myreg = /^(((13[0-9]{1})|(15[0-9]{1})|(18[0-9]{1})|(17[0-9]{1}))+\d{8})$/
  console.log(props.cellPhone)
  if (props.cellPhone == '') {
    uni.showToast({
      title: '请输入您的手机号',
      duration: 2000,
      icon: 'none',
    })
    return
  }
  if (!myreg.test(props.cellPhone)) {
    uni.showToast({
      title: '手机号格式不正确',
      duration: 2000,
      icon: 'none',
    })
    return
  }
  // 请求验证码
  getCode()
  status.value = false
  timeInterval = setInterval(() => {
    if (time.value <= 0) {
      clearInterval(timeInterval)
    }
    time.value--
  }, 1000)
}
function getCode() {
  getVerificationCode({
    url: props.url,
    loginName: props.account,
    cellphone: props.cellPhone,
  }).then((res) => {
    if (res && res.success) {
      return
    } else {
      status.value = true
      clearInterval(timeInterval)
      uni.showToast({
        title: res.message,
        duration: 2500,
        icon: 'none',
      })
    }
  })
}
watch(time, (newValue, oldValue) => {
  if (newValue <= 0) {
    clearInterval(timeInterval)
    status.value = true
  }
})
</script>
<style></style>
