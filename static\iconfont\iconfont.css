@font-face {
  font-family: 'iconfont';
  font-weight: normal;
  font-style: normal;
  src: url('./iconfont.ttf') format('truetype');
  font-display: swap;
}
.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-right:before {
  content: '\e665';
}

.icon-calendar:before {
  content: '\e667';
}

.icon-comment:before {
  content: '\e669';
}

.icon-data-view:before {
  content: '\e66a';
}

.icon-explain:before {
  content: '\e66e';
}

.icon-file:before {
  content: '\e671';
}

.icon-close:before {
  content: '\e668';
}

.icon-select:before {
  content: '\e67e';
}

.icon-zhuti:before {
  content: '\e682';
}

.icon-gangwei:before {
  content: '\e658';
}

.icon-yinsixieyi:before {
  content: '\e600';
}

.icon-guanyuwomen:before {
  content: '\e69a';
}

.icon-xieyi:before {
  content: '\e66c';
}

.icon-bianjidaimashili:before {
  content: '\e62d';
}

.icon-shouye:before {
  content: '\e604';
}
