/*将数字转换为货币
 *@param num - 要格式化的数字。
 *@param currency——要格式化的货币——默认设置为“元”，因为我主要需要处理基于人民币的价格。
 *@param locale — 默认设置为“zh-CN”，因为我经常处理显示为中国的价格。
 **/

function convertToCurrency(num, currency = 'CNY', locale = 'zh-CN') {
  const formatter = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
  })

  return formatter.format(num)
}

/*将 HTML 字符串转换为 DOM 对象
 * @param content — 要转换为对象的 HTML 字符串。
 * @param selector——你想要接收的选择器——DOMParser 对象将创建一个完整的 HTMLObject，包括一个 <html> 和 <body> 标记。
 * 如果你只想要某个元素，你可以传入选择器，例如“section”，你将收到该元素。
 * */

function parseStringAsHtml(content, selector) {
  const domParser = new DOMParser()
  const parsed = domParser.parseFromString(content, 'text/html')

  return parsed.querySelector(selector)
}

/*去抖
 *@param fn - 要执行的函数。
 *@param wait - 函数执行前的等待时间。
 *@param immediate -  一个布尔值，指示第一次调用函数时是否应立即调用。
 * */

function debounce(fn, wait, immediate) {
  let timeout
  return function setDebounce(...args) {
    const later = () => {
      timeout = null
      if (!immediate) {
        fn.apply(this, args)
      }
    }
    const callNow = immediate && !timeout
    clearTimeout(timeout)
    timeout = setTimeout(later, wait || 200)
    if (callNow) {
      fn.apply(this, args)
    }
  }
}
// 日期验证
//@param date  日期

function isDateValid(date) {
  return !Number.isNaN(date.getTime())
}

//将 FormData 转换为 JSON
// @param formData ：表单对象
function convertFormdataToJsonObject(formData) {
  const data = {}
  for (const [key, value] of formData.entries()) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const oldValue = data[key]
      if (!Array.isArray(data[key])) {
        data[key] = []
        data[key].push(oldValue)
      }
      data[key].push(value)
      continue
    }
    data[key] = value
  }
  return data
}

// 衡量一个函数的性能
/*
 *@param name — 标签的名称显示在控制台中。
 *@param fn - 您要衡量其性能的函数。
 *@param 任何附加参数——你正在调用的函数的参数。
 * */
function measurePerformance(name, fn, ...args) {
  if (typeof fn !== 'function') {
    console.error(`Provide a valid function, ${typeof fn} provided`)
    return
  }
  console.time(name)
  fn.bind(this, ...args)
  console.timeEnd(name)
}
/*从数组中删除重复项*/
function removeDuplicates(array) {
  if (!Array.isArray(array)) {
    console.error(`array expected, ${typeof array} provided`)
    return
  }
  return [...new Set(array)]
}
module.exports = {
  removeDuplicates: removeDuplicates,
  measurePerformance: measurePerformance,
  convertFormdataToJsonObject: convertFormdataToJsonObject,
  convertToCurrency: convertToCurrency,
  parseStringAsHtml: parseStringAsHtml,
  debounce: debounce,
  isDateValid: isDateValid,
}
