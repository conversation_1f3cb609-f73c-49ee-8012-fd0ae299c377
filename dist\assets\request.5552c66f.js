import{A as e,G as a,H as t,Y as r,J as n,aP as s,Q as o,aQ as i}from"./index-0e73d719.js";import{i as u,a as l,b as c}from"./uniUtils.075f77fd.js";const d=function(){let e="UNI_APP";return e="H5","H5"}(),h="/auth/wechatlogin",p="/exchange/wechat/getCaptchaByWechatBind",f="/exchange/wechat/wechatBind",m="/user/orgs/upAndDown",k="/user/staffOrgs",g="/cms/v1.0/notice/selectTop5Page",T="/user/dict/data/list/notice_type_list",y="/cms/v1.0/notice/selectForPage",v="/cms/v1.0/notice/selectNoticeByNoticeId",P="/cms/v1.0/advertising/selectTopPage",_="/cms/v1/files/single";function w(e){return L({url:"/auth/login",method:"POST",params:e})}function b(e){return e.uniContentType="json",L({url:"/auth/logout",method:"POST",params:e})}function C(e){return L({url:"/auth/oauth/check_token",method:"GET",params:e})}function x(i,c){return async function(i={}){let c=Number(r.get("refreshTokenNum",!0));if(c>=3)return n({url:"/pages/login/login"}),void r.set("refreshTokenNum",0);c+=1,r.set("refreshTokenNum",c);try{const c=await new Promise(((r,n)=>{W=a(),B=t(),i.url=`${E}${i.url}`,i.data=i.params,i.header={clientId:e.clientId,"Content-Type":"application/x-www-form-urlencoded",scope:d},B.hasLogin&&(W.value&&(U=W.value),W.tokenType&&(V=W.tokenType),i.header.Authorization=`${V} ${U}`),r(i)}));return new Promise(((e,a)=>{c.success=t=>{if(l(t.data)&&u(t.data)&&t.data.hasOwnProperty("code")){switch(Number(t.data.code)){case 1:case 0:e(t.data);break;case 402:default:a(t.data.message);break;case 401:x({grant_type:"refresh_token",client_id:"bVS46ElU",client_secret:W.client_secret,refresh_token:W.refreshToken.value},c).then((e=>{W.$patch((a=>{a.refreshToken=e.data.refreshToken,a.tokenType=e.data.tokenType,a.value=e.data.value,a.expiration=e.data.expiration}))}))}}else l(t.data)?u(t.data)&&("unauthorized"==t.data.error&&n({url:"/pages/generalPage/noauthority/noauthority"}),"invalid_token"==t.data.error&&(r.set("refreshTokenNum",0),o({title:"登录信息已失效，请重新登录",icon:"none",success:()=>{B.logout(),W.clearAuthInfo(),setTimeout((()=>{n({url:"/pages/login/login",success:()=>{}})}),2e3)}})),e(t.data)):a("返回数据异常")},c.fail=e=>{a(e)},s(c)}))}catch(h){return Promise.reject(h)}}({url:"/auth/oauth/token",method:"POST",params:i})}function O(e){return L({url:"/auth/jobSelect",method:"POST",params:e})}function j(e){l(e)||(e={});let a="/user/staffs/getVerificationCode";return u(e)&&(e.uniContentType="json",e.hasOwnProperty("getVerificationCodeType")||(e.getVerificationCodeType="1"),e.newPassWord="",e.verificationCode="",e.hasOwnProperty("url")&&!c(a)&&(a=e.url),delete e.url),L({url:a,method:"POST",params:e})}function N(e){return l(e)||(e={}),u(e)&&(e.uniContentType="json"),L({url:"/user/staffs/forgottenPassword",method:"POST",params:e})}function $(e){return L({url:"/user/tenants/info",method:"GET",params:e})}function S(e){return e.uniContentType="json",L({url:"/user/common/clientConfig",method:"POST",params:e})}function I(e){return D({url:"/cms/v1/files/single",params:e})}function A(e){return e.uniContentType="json",L({url:"/user/layout-design/getEnableLayoutDesign",method:"POST",params:e})}let E=e.BASEURL,U="",V="",z=E+"/auth/captcha";var W,B;async function L(o={}){try{const i=await new Promise(((r,n)=>{W=a(),B=t(),-1==o.url.search("http")&&(o.url=`${E}${o.url}`),l(o.params)&&l(o.params.uniContentType)&&"json"==o.params.uniContentType?(o.header={clientId:e.clientId,"Content-Type":"application/json;charset=UTF-8",scope:d,...o.header},delete o.params.uniContentType):o.header={clientId:e.clientId,"Content-Type":"application/x-www-form-urlencoded",scope:d,...o.header},l(o.params)&&(o.data=o.params),B.hasLogin&&(W.value&&(U=W.value),W.tokenType&&(V=W.tokenType),o.header.Authorization=`${V} ${U}`),c(B.jPushregisterId)||(o.header.jPushregisterId=B.jPushregisterId),o.sslVerify=!1,r(o)}));return new Promise(((e,a)=>{i.success=t=>{if(l(t.data)&&u(t.data)&&t.data.hasOwnProperty("code")){switch(Number(t.data.code)){case 1:case 0:e(t.data);break;case 402:default:a(t.data.message);break;case 401:if(i.url&&i.url.endsWith("/auth/login")){a(t.data.message);break}x({grant_type:"refresh_token",client_id:"bVS46ElU",client_secret:W.client_secret,refresh_token:W.refreshToken.value}).then((a=>{W.$patch((t=>{if(t.refreshToken=a.data.refreshToken,t.tokenType=a.data.tokenType,t.value=a.data.value,t.expiration=a.data.expiration,r.set("refreshTokenNum",0),-1!=o.url.search("/auth/oauth/check_token")){o.data.token=a.data.value,o.params.token=a.data.value;let e=o.url.indexOf("?"),t=o.url.substring(0,e);o.url=t+"?token="+a.data.value}let n=L(o);e(n)}))}))}}else if(l(t.data)&&u(t.data)&&t.hasOwnProperty("statusCode")){switch(Number(t.statusCode)){case 200:e(t.data);break;case 402:default:a(t.data.message);break;case 400:case 401:if(i.url&&i.url.endsWith("/auth/login")){a(t.data.message);break}x({grant_type:"refresh_token",client_id:"bVS46ElU",client_secret:W.client_secret,refresh_token:W.refreshToken.value}).then((a=>{W.$patch((t=>{if(t.refreshToken=a.data.refreshToken,t.tokenType=a.data.tokenType,t.value=a.data.value,t.expiration=a.data.expiration,r.set("refreshTokenNum",0),-1!=o.url.search("/auth/oauth/check_token")){o.data.token=a.data.value,o.params.token=a.data.value;let e=o.url.indexOf("?"),t=o.url.substring(0,e);o.url=t+"?token="+a.data.value}let n=L(o);e(n)}))}))}}else if(l(t.data)){if(u(t.data))if("unauthorized"==t.data.error)n({url:"/pages/generalPage/noauthority/noauthority"});else if("invalid_token"==t.data.error){if(i.url&&i.url.endsWith("/auth/login"))return void a(t.data.message);x({grant_type:"refresh_token",client_id:"bVS46ElU",client_secret:W.client_secret,refresh_token:W.refreshToken.value}).then((a=>{W.$patch((t=>{if(t.refreshToken=a.data.refreshToken,t.tokenType=a.data.tokenType,t.value=a.data.value,t.expiration=a.data.expiration,r.set("refreshTokenNum",0),-1!=o.url.search("/auth/oauth/check_token")){o.data.token=a.data.value,o.params.token=a.data.value;let e=o.url.indexOf("?"),t=o.url.substring(0,e);o.url=t+"?token="+a.data.value}let n=L(o);e(n)}))}))}}else a("返回数据异常")},i.fail=e=>{a(e)},s(i)}))}catch(i){return Promise.reject(i)}}async function D(r={}){try{const s=await new Promise(((n,s)=>{W=a(),B=t(),r.url=`${E}${r.url}`,l(r.params)&&(r.filePath=r.params.filePath,r.name="multipartFile",r.formData={appCode:e.appCode}),r.header={clientId:e.clientId,scope:d},B.hasLogin&&(W.value&&(U=W.value),W.tokenType&&(V=W.tokenType),r.header.Authorization=`${V} ${U}`),n(r)}));return new Promise(((e,a)=>{s.success=t=>{let r=JSON.parse(t.data);if(l(r)&&u(r)&&r.hasOwnProperty("code")){switch(Number(r.code)){case 1:case 0:e(r);break;case 402:a(r.message);break;case 401:if(s.url&&s.url.endsWith("/auth/login")){a(r.message);break}x({grant_type:"refresh_token",client_id:"bVS46ElU",client_secret:W.client_secret,refresh_token:W.refreshToken.value}).then((e=>{W.$patch((a=>{a.refreshToken=e.data.refreshToken,a.tokenType=e.data.tokenType,a.value=e.data.value,a.expiration=e.data.expiration}))}));default:a(r.message)}}else l(r)?u(r)&&("unauthorized"==r.error&&n({url:"/pages/generalPage/noauthority/noauthority"}),e(r)):a("返回数据异常")},s.fail=e=>{a(e)},i(s)}))}catch(s){return Promise.reject(s)}}export{P as A,g as N,d as P,p as a,f as b,h as c,S as d,$ as e,A as f,C as g,b as h,z as i,j,N as k,w as l,T as m,y as n,v as o,m as p,k as q,L as r,O as s,I as t,D as u,_ as v};
