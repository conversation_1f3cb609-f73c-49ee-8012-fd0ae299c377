import{q as e,r as a,b as t,o as s,c as l,w as r,n as c,f as o,j as d,t as u,ad as f,l as n,k as i}from"./index-4d380fda.js";import{_ as p}from"./u-icon.66912310.js";import{o as h,E as m}from"./staticUrl.6af4fd3e.js";import{_}from"./_plugin-vue_export-helper.1b428a4d.js";const g=_({__name:"yuni-title",props:{title:{type:String,default:"标题名称"},bjImg:{type:String,default:h},type:{type:String,default:"short"},colors:{type:String,default:"light"}},setup(h){e({back1:m});const _=h;function g(){f({delta:1})}return(e,f)=>{const h=n,m=i,y=a(t("u-icon"),p);return s(),l(m,null,{default:r((()=>["home"==_.type?(s(),l(m,{key:0,class:"header",style:c({backgroundImage:"url("+_.bjImg+")"})},{default:r((()=>[o(m,{class:"header-top"},{default:r((()=>[o(h,{class:"header-back"}),o(h,{class:"header-text"},{default:r((()=>[d(u(_.title),1)])),_:1}),o(h,{class:"header-back"})])),_:1})])),_:1},8,["style"])):"long"==_.type?(s(),l(m,{key:1,class:"header-long",style:c({backgroundImage:"url("+_.bjImg+")"})},{default:r((()=>[o(m,{class:"header-top"},{default:r((()=>[o(y,{class:"header-back",name:"arrow-left",size:"25",color:"#fff",onClick:f[0]||(f[0]=e=>g())}),o(h,{class:"header-text"},{default:r((()=>[d(u(_.title),1)])),_:1}),o(h,{class:"header-back"})])),_:1})])),_:1},8,["style"])):(s(),l(m,{key:2,class:"header",style:c({backgroundImage:"url("+_.bjImg+")"})},{default:r((()=>[o(m,{class:"header-top"},{default:r((()=>[o(y,{class:"header-back",name:"arrow-left",size:"25",color:"#fff",onClick:f[1]||(f[1]=e=>g())}),o(h,{class:"header-text"},{default:r((()=>[d(u(_.title),1)])),_:1}),o(h,{class:"header-back"})])),_:1})])),_:1},8,["style"]))])),_:1})}}},[["__scopeId","data-v-3c7fc1e7"]]);export{g as _};
