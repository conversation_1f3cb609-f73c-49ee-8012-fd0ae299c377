<!-- 公告组件：可上下滚动切换 -->
<template>
  <view class="box">
    <view v-if="data.isShow" class="an-notice-box">
      <view class="notice">
        <u--image
        width="54rpx"
        height="47rpx"
        mode="scaleToFill"
        :src='zxgg'></u--image>
      </view>
      <view class="an-notice-content">
        <u-notice-bar
          :text="titleData"
		  icon=""
		  mode=""
          color="#666666"
          :bgColor="props.bgColor"
          direction="column"
          duration="3000"
          speed="80"
          @click="goDetail"
        ></u-notice-bar>
      </view>
	  <u-icon class="right-link" @click="goList" name="arrow-right"></u-icon>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { COMMON_HOME_ZXGG_BANNER_URL,GOICON_URL } from '@/common/net/staticUrl.js'
const props = defineProps({
  list: {
    type: Array,
    default: [],
  },
  color: {
    type: String,
    default: '#de8c17',
  },
  bgColor: {
    type: String,
    default: '#fff',
  },
  switchTime: {
    type: Number,
    default: 3,
  },
  showSerial: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['more', 'onNoticeItemClick', 'toNextList'])
console.log('props', props.list)
const data = reactive({
  number: 0,
  selfList: props.list ? props.list : [],
  isShow: true,
  goIcon_image: GOICON_URL,
})
let zxgg = COMMON_HOME_ZXGG_BANNER_URL;
// 通知标题集合
var titleData = []
data.selfList.forEach((element) => {
  titleData.push(element.title)
})
startMove()
function startMove() {
  let timer = setTimeout(() => {
    if (data.number === data.selfList.length - 1) {
      data.number = 0
    } else {
      data.number += 1
    }
    startMove()
  }, props.switchTime * 1000)
}
function more() {
  emit('more')
}
function goDetail(item) {
  if (typeof item === 'number') {
    emit('onNoticeItemClick', [item])
  }
}
function goList() {
  emit('toNextList')
}
function closeNoticeBar() {
  data.isShow = false
}
</script>

<style lang="scss" scoped>
.an-notice-box {
  width: 710rpx;
  height: 70rpx;
  border-radius: 20rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.notice {
  height: 47rpx;
  margin: 0rpx 10rpx 0 30rpx;
  flex: 1;
}

.an-notice-content {
  line-height: 150rpx;
  position: relative;
  font-size: 26rpx;
  flex: 2 1 80%;
}

.right-link {
	flex: 1;
	padding: 0 6rpx 0 0;
}

@keyframes anotice {
  0% {
    transform: translateY(80%);
  }
  30% {
    transform: translateY(0);
  }
  70% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100%);
  }
}
.an-notice-content-item {
  animation: anotice 3s linear infinite;
  height: 50rpx;
}
.an-notice-content-item-text {
  display: block;
  line-height: 148rpx;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.more {
  height: 100%;
  line-height: 148rpx;
  margin-right: 30rpx;
}
</style>
