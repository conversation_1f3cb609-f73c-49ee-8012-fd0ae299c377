	// 拨打电话
	// #ifdef APP-PLUS
		import permission from '@/common/app-plus/permission'
		import kvStore from '@/common/store/uniKVStore.js'
	// #endif
	// phone:电话号码，body:短信内容
	export default async function sendSms(phone,body) {
		// #ifdef MP-WEIXIN
			wx.sendSms({
			  phoneNumber: phone,
			  complete: res => {
				console.log(res)
			  }
			})
		// #endif
		// #ifdef H5
			//获取设备类型
			let platform = uni.getSystemInfoSync().platform
			window.location.href = platform === 'ios' ? `sms:/open?addresses=${phone}&body=${body}` :
				`sms:${phone}?body=${body}`
		// #endif
		// #ifdef APP-PLUS
		if(uni.getSystemInfoSync().platform == "android"){
			let authSendsmg =  kvStore.get('send_sms', true)
			console.log('本地短信权限状态',authSendsmg);
			if(authSendsmg){
				let status = permission.isIOS ?
					await permission.requestIOS('send_sms') :
					await permission.requestAndroid('android.permission.SEND_SMS')
				if(status == 1){
					let message = plus.messaging.createMessage(plus.messaging.TYPE_SMS)
					message.to = [phone] //这里数组中需要是字符串,否则ios会出现空白bug
					message.body = body
					plus.messaging.sendMessage(message)
				}else if(status == 0){
					return
				}else {
					permission.judgePermission("send_sms",(res)=>{
						console.log("judgeResult=====send_sms"+JSON.stringify(res))
					});
				}
			}else {
				uni.showModal({
				  title: '权限申请',
				  content: '为了正常发送短信、彩信，读取短信、彩信等功能，请允许使用短信权限。你可以通过系统“设置”进行权限的管理',
				  confirmText: '继续',
				  cancelText: '关闭',
				  success: async (r) => {
					if (r.confirm) {
						let status = permission.isIOS ?
							await permission.requestIOS('send_sms') :
							await permission.requestAndroid('android.permission.SEND_SMS')
						if(status == 1){
							kvStore.set('send_sms', true)
							let message = plus.messaging.createMessage(plus.messaging.TYPE_SMS)
							message.to = [phone] //这里数组中需要是字符串,否则ios会出现空白bug
							message.body = body
							plus.messaging.sendMessage(message)
						}else if(status == 0){
							return
						}else {
							kvStore.set('send_sms', true)
							permission.judgePermission("send_sms",(res)=>{
								console.log("judgeResult=====send_sms"+JSON.stringify(res))
							});
						}
					} else if (r.cancel) {
						return
					}
				  }
				})
			}
			}else {
				plus.messaging.sendMessage(message)
			}
		// #endif
	}