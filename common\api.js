import { refresh, request, uploadFile, downloadFile } from './net/request.js'
import { joinUrlParams } from '@/common/uniUtils.js'
import { isStrEmpty, isDef, isObj } from '@/common/uniUtils.js'
import {
  API_LOGIN_URL,
  API_LOGOUT_URL,
  API_USERINFO_URL,
  API_TRAREN_URL,
  API_REFRESH_URL,
  API_SWITCHJOB_URL,
  API_VERFICATION_URL,
  API_FORGOT_URL,
  API_SYSTEM_URL,
  API_DINGLOGIN_URL,
  API_WORK_URL,
  API_KNOWLEDGE_URL,
  API_AGREEMENT_URL,
  API_COMMONRESOURCES_URL,
  API_JPUSHUSER_URL,
  API_FILEUPLOAD_URL,
  API_FILEDOWNLOAD_URL,
  API_DINGAUTHLOGIN_URL,
  API_DINGJSAPI_URL,
  API_DING_NOTIFICATION_URL,
  API_LAYOUT_DESIGN,
  API_USER_DICT_DATA
} from '@/common/net/netUrl.js'
// 登录
export function login(params) {
  return request({
    url: API_LOGIN_URL,
    method: 'POST',
    params,
  })
}
// 登出
export function logout(params) {
  console.log(params)
  params['uniContentType'] = 'json'
  return request({
    url: API_LOGOUT_URL,
    method: 'POST',
    params,
  })
}
//获取个人信息
export function getUserInfo(params) {
  return request({
    url: API_USERINFO_URL,
    method: 'GET',
    params,
  })
}
//刷新token
export function refreshToken(params, back) {
  // console.log('刷新token请求~~',params)
  return refresh(
    {
      url: API_REFRESH_URL,
      method: 'POST',
      params,
    },
    back
  )
}

//切换岗位
export function switchJobTitle(params) {
  console.log(params)
  return request({
    url: API_SWITCHJOB_URL,
    method: 'POST',
    params,
  })
}

// 获取短信验证码
// cellphone: "17668860527"
// loginName: "eric"
// getVerificationCodeType (可选参数)，默认值为1；
export function getVerificationCode(params) {
  if (!isDef(params)) {
    params = {}
  }
  let url = API_VERFICATION_URL
  if (isObj(params)) {
    params['uniContentType'] = 'json'
    if (!params.hasOwnProperty('getVerificationCodeType')) {
      params['getVerificationCodeType'] = '1'
    }
    params['newPassWord'] = ''
    params['verificationCode'] = ''
    if (params.hasOwnProperty('url') && !isStrEmpty(url)) {
      url = params['url']
    }
    delete params.url
  }
  console.log(params)
  return request({
    url: url,
    method: 'POST',
    params,
  })
}
// 找回密码
// cellphone: "17668860527"
// loginName: "eric"
// newPassWord: "04bd3531601c30f02fc66ee8be20d112ad3f2e2f17e4d8f3a87a49c1a6a6a517dff30aeab225e8fbd646ea42019ab6ff00062a426bc065c33849395f0fa7bcef7cfc5b7cd0572cef41e52756985af55e81ef82953770e8626bfe4a3f9c002e5a57014de41ca32e6481b000bd"
// verificationCode: "666666"
export function forgottenPassword(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['uniContentType'] = 'json'
  }
  console.log(params)
  return request({
    url: API_FORGOT_URL,
    method: 'POST',
    params,
  })
}

//APP检查更新
export function systemUpdate(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: API_SYSTEM_URL,
    method: 'POST',
    params,
  })
}
//钉钉授权登录
export function dingLogin(params) {
  console.log(params)
  return request({
    url: API_DINGLOGIN_URL,
    method: 'GET',
    params,
  })
}
//获取租户信息  tenantLoginName
export function getTrarenInfo(params) {
  console.log(params)
  return request({
    url: API_TRAREN_URL,
    method: 'GET',
    params,
  })
}
//工作台
export function getWork(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['uniContentType'] = 'json'
  }
  console.log(params)
  return request({
    url: API_WORK_URL,
    method: 'post',
    params,
  })
}
//知识库
export function knowledge(params) {
  console.log(params)
  return request({
    url: API_KNOWLEDGE_URL,
    method: 'GET',
    params,
  })
}
//隐私协议 用户协议
export function agreement(params) {
  console.log(params)
  params['uniContentType'] = 'json'
  return request({
    url: API_AGREEMENT_URL,
    method: 'POST',
    params,
  })
}

//未登录的公共资源
export function commonResources(params) {
  console.log(params)
  params['uniContentType'] = 'json'
  return request({
    url: API_COMMONRESOURCES_URL,
    method: 'POST',
    params,
  })
}

//极光推送绑定用户
export function jpushUser(pathParams) {
  console.log('jpushUser', pathParams)
  let localUrl = API_JPUSHUSER_URL + '/' + pathParams.registerationId
  return request({
    url: localUrl,
    method: 'POST',
  })
}
//文件上传
export function fileUpload(params) {
  return uploadFile({
    url: API_FILEUPLOAD_URL,
    params,
  })
}
//文件下载
export function fileDownload(pathParams) {
  let localUrl = API_FILEDOWNLOAD_URL + '/' + pathParams.fileId
  return downloadFile({
    url: localUrl,
  })
}
//钉钉H5授权登录获取authcode
export function dingAuthLogin(pathParams) {
  console.log(pathParams)
  let localUrl = joinUrlParams(API_DINGAUTHLOGIN_URL, pathParams)
  return request({
    url: localUrl,
    method: 'GET',
  })
}
//工作台动态布局获取数据
export function getLyoutData(params) {
  console.log(params)
  params['uniContentType'] = 'json'
  return request({
    url: API_LAYOUT_DESIGN,
    method: 'POST',
    params,
  })
}
//钉钉H5鉴权获取签名
export function getJsticket(pathParams) {
  console.log(pathParams)
  let localUrl = joinUrlParams(API_DINGJSAPI_URL, pathParams)
  return request({
    url: localUrl,
    method: 'GET',
  })
}
//钉钉H5发送消息通知
export function getworkNotification(params) {
  console.log(params)
  params['uniContentType'] = 'json'
  return request({
    url: API_DING_NOTIFICATION_URL,
    method: 'POST',
    params
  })
}

// 获取字典

export function getDictInfo(dictCode) {
  return request({
    url: API_USER_DICT_DATA + dictCode,
    method: 'GET',
  })
}
