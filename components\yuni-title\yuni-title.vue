<template>
	<view>
		<view class="header" :style=" { backgroundImage: 'url('+props.bjImg+')'}" v-if="props.type == 'home'">
			<view class="header-top">
				<text class="header-back"></text>
				<text class="header-text">{{props.title}}</text>
				<text class="header-back"></text>
			</view>
		</view>
		<view class="header-long" :style=" { backgroundImage: 'url('+props.bjImg+')'}" v-else-if="props.type == 'long'">
			<view class="header-top">
				<u-icon class="header-back" name="arrow-left" size="25" color="#fff" @click="back()" ></u-icon>
				<text class="header-text">{{props.title}}</text>
				<text class="header-back"></text>
			</view>
		</view>
		<view class="header" style="background-color: #c20000;" v-else>
			<view class="header-top">
				<u-icon class="header-back" name="arrow-left" size="25" color="#fff" @click="back()" ></u-icon>
				<text class="header-text">{{props.title}}</text>
				<text class="header-back"></text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {reactive} from 'vue'
	import { onShow } from '@dcloudio/uni-app'
	import {COMMON_TITLE_BRCKGROUND_URL,EYESHAPE_URL} from "@/common/net/staticUrl.js"
	
	const data = reactive({
		back1: EYESHAPE_URL,
	})
	
	const props = defineProps({
	  title: {
	    type: String,
	    default: '标题名称',
	  },
	  bjImg: {//导航栏长短样式：默认短导航栏COMMON_TITLE_BRCKGROUND_URL，长导航栏COMMON_TITLE_BRCKGROUND_LONG_URL
	    type: String,
	    default: COMMON_TITLE_BRCKGROUND_URL,
	  },
	  type: {//short为短标题  long为长标题  home为不带返回按钮
	    type: String,
	    default: 'short',
	  },
	  colors: {//设置系统状态栏样式 默认浅色"light"  深色'dark'  
		  type: String,
		  default: 'light',
	  }
	})
	
	// btColor()
	// function btColor(){
	// 	// #ifdef APP-PLUS
	// 	plus.navigator.setStatusBarStyle('dark');
	// 	// #endif
	// }
	
	function back(){
		uni.navigateBack({
			delta: 1
		});
	}
</script>

<style lang="less">
.header{
	width: 100%;
	/* #ifdef MP-WEIXIN */
	height: 160rpx;
	/* #endif */
	/* #ifdef H5 */
	height: 88rpx;
	/* #endif */
	/* #ifdef APP-PLUS */
	height: 132rpx;
	/* #endif */
	overflow-y: hidden;
	background-size: cover;
	position: fixed;
	top: 0;
}
.header-long{
	/* #ifdef H5 */
	width: 100%;
	/* #endif */
	/* #ifndef H5 */
	width: 750rpx;
	/* #endif */
	height: 100vh;
	overflow-y: hidden;
	background-size: cover;
	position: relative;
	z-index: -1;
	top: 0;
}
.header-top{
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	/* #ifdef MP-WEIXIN */
	padding-top: 80rpx;
	/* #endif */
	/* #ifdef APP-PLUS */
	padding-top: 60rpx;
	/* #endif */
	/* #ifdef H5 */
	padding-top: 7rpx;
	/* #endif */
}
.header-back{
	padding: 12rpx;
	width: 48rpx;
	height: 48rpx;
}
.header-text{
	display: flex;
	justify-content: center;
	color: #fff;
	font-size: 36rpx;
	font-family: PingFangSC-Medium, PingFang SC;
}
</style>