import {
	defineConfig
} from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import esbuild from 'rollup-plugin-esbuild'
export default defineConfig({
	plugins: [uni(),
		// 解决钉钉H5不支持可选链语法问题
		{
			...esbuild({
				target: 'chrome70',
				include: [/\.vue$/, /\.js$/],
				loaders: {
					'.vue': 'js'
				}
			}),
			enforce: 'post'
		}
	],
	  resolve: {
	    alias: {
	      'diagram-js/lib/navigation/touch': 'diagram-js/lib/navigation/touch', // 如果需要，可以指定具体路径
	    },
	  },
	// 配置代理
	server: {
		port: 8080,
		proxy: {
			'/prod-api': {
				target: 'http://172.16.11.82:9090',
				// target:'http://172.16.2.187:8080/',
				// target:'https://cuiyuanzhen.jsbnb.top',
				changeOrigin: false, //开启代理，允许跨域
				// rewrite: path => path.replace(/^\/prod-api/, '') // 设置重写的路径（意思是将代理部分路由（/prod-api）设置为空）
			}
		},
	},
	transpileDependencies: ['uview-plus'],
	// 发布时删除console
	build: {
		minify: 'terser',
		terserOptions: {
			compress: {
				drop_console: true,
			},
		},
		// 确保静态资源正确复制
		assetsDir: 'static',
		rollupOptions: {
			output: {
				// 确保字体文件等静态资源的正确输出
				assetFileNames: (assetInfo) => {
					const info = assetInfo.name.split('.');
					const extType = info[info.length - 1];
					if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
						return `static/media/[name].[hash][extname]`;
					}
					if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
						return `static/images/[name].[hash][extname]`;
					}
					if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
						// 保持字体文件原始路径，不添加hash
						if (assetInfo.name.includes('iconfont')) {
							return `static/iconfont/[name][extname]`;
						}
						if (assetInfo.name.includes('iconfonts')) {
							return `static/iconfonts/[name][extname]`;
						}
						return `static/fonts/[name].[hash][extname]`;
					}
					return `static/[ext]/[name].[hash][extname]`;
				},
			},
		},
	}
})