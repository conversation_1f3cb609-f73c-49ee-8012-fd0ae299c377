import{d as e,m as t,a,o,c as l,w as r,j as s,t as i,s as n,n as u,g as d,l as c,r as b,b as h,f as p,h as m,F as f,e as g,k as y,M as v,U as _,i as x,u as S,a3 as C,B as I}from"./index-4d380fda.js";import{_ as k}from"./u-icon.66912310.js";import{_ as $}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as w}from"./u-safe-bottom.c99871d2.js";const B=$({name:"u-badge",mixins:[t,{props:{isDot:{type:Boolean,default:e.badge.isDot},value:{type:[Number,String],default:e.badge.value},modelValue:{type:[Number,String],default:e.badge.modelValue},show:{type:Boolean,default:e.badge.show},max:{type:[Number,String],default:e.badge.max},type:{type:String,default:e.badge.type},showZero:{type:Boolean,default:e.badge.showZero},bgColor:{type:[String,null],default:e.badge.bgColor},color:{type:[String,null],default:e.badge.color},shape:{type:String,default:e.badge.shape},numberType:{type:String,default:e.badge.numberType},offset:{type:Array,default:e.badge.offset},inverted:{type:Boolean,default:e.badge.inverted},absolute:{type:Boolean,default:e.badge.absolute}}},a],computed:{boxStyle:()=>({}),badgeStyle(){const e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){const t=this.offset[0],a=this.offset[1]||t;e.top=uni.$u.addUnit(t),e.right=uni.$u.addUnit(a)}return e},showValue(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}}}},[["render",function(e,t,a,b,h,p){const m=c;return e.show&&(0!==Number(e.value)||e.showZero||e.isDot)?(o(),l(m,{key:0,class:n([[e.isDot?"u-badge--dot":"u-badge--not-dot",e.inverted&&"u-badge--inverted","horn"===e.shape&&"u-badge--horn",`u-badge--${e.type}${e.inverted?"--inverted":""}`],"u-badge"]),style:u([e.$u.addStyle(e.customStyle),p.badgeStyle])},{default:r((()=>[s(i(e.isDot?"":p.showValue),1)])),_:1},8,["class","style"])):d("",!0)}],["__scopeId","data-v-657a84d9"]]);const D=$({name:"u-tabbar-item",mixins:[t,a,{props:{name:{type:[String,Number,null],default:e.tabbarItem.name},icon:{icon:String,default:e.tabbarItem.icon},badge:{type:[String,Number,null],default:e.tabbarItem.badge},dot:{type:Boolean,default:e.tabbarItem.dot},text:{type:String,default:e.tabbarItem.text},badgeStyle:{type:[Object,String],default:e.tabbarItem.badgeStyle}}}],data:()=>({isActive:!1,parentData:{value:null,activeColor:"",inactiveColor:""}}),options:{virtualHost:!0},created(){this.init()},emits:["click","change"],methods:{init(){this.updateParentData(),this.parent||uni.$u.error("u-tabbar-item必须搭配u-tabbar组件使用");const e=this.parent.children.indexOf(this);this.isActive=(this.name||e)===this.parentData.value},updateParentData(){this.getParentData("u-tabbar")},updateFromParent(){this.init()},clickHandler(){this.$nextTick((()=>{const e=this.parent.children.indexOf(this),t=this.name||e;t!==this.parent.value&&this.parent.$emit("change",t),this.$emit("click",t)}))}}},[["render",function(e,t,a,n,d,v){const _=b(h("u-icon"),k),x=b(h("u-badge"),B),S=y,C=c;return o(),l(S,{class:"u-tabbar-item",style:u([e.$u.addStyle(e.customStyle)]),onClick:v.clickHandler},{default:r((()=>[p(S,{class:"u-tabbar-item__icon"},{default:r((()=>[e.icon?(o(),l(_,{key:0,name:e.icon,color:d.isActive?d.parentData.activeColor:d.parentData.inactiveColor,size:20},null,8,["name","color"])):(o(),m(f,{key:1},[d.isActive?g(e.$slots,"active-icon",{key:0},void 0,!0):g(e.$slots,"inactive-icon",{key:1},void 0,!0)],64)),p(x,{absolute:"",offset:[0,e.dot?"34rpx":e.badge>9?"14rpx":"20rpx"],customStyle:e.badgeStyle,isDot:e.dot,value:e.badge||(e.dot?1:null),show:e.dot||e.badge>0},null,8,["offset","customStyle","isDot","value","show"])])),_:3}),g(e.$slots,"text",{},(()=>[p(C,{class:"u-tabbar-item__text",style:u({color:d.isActive?d.parentData.activeColor:d.parentData.inactiveColor})},{default:r((()=>[s(i(e.text),1)])),_:1},8,["style"])]),!0)])),_:3},8,["style","onClick"])}],["__scopeId","data-v-005e9f17"]]);const N=$({name:"u-tabbar",mixins:[t,a,{props:{value:{type:[String,Number,null],default:e.tabbar.value},safeAreaInsetBottom:{type:Boolean,default:e.tabbar.safeAreaInsetBottom},border:{type:Boolean,default:e.tabbar.border},zIndex:{type:[String,Number],default:e.tabbar.zIndex},activeColor:{type:String,default:e.tabbar.activeColor},inactiveColor:{type:String,default:e.tabbar.inactiveColor},fixed:{type:Boolean,default:e.tabbar.fixed},placeholder:{type:Boolean,default:e.tabbar.placeholder}}}],data:()=>({placeholderHeight:0}),computed:{tabbarStyle(){const e={zIndex:this.zIndex};return uni.$u.deepMerge(e,uni.$u.addStyle(this.customStyle))},updateChild(){return[this.value,this.activeColor,this.inactiveColor]},updatePlaceholder(){return[this.fixed,this.placeholder]}},watch:{updateChild(){this.updateChildren()},updatePlaceholder(){this.setPlaceholderHeight()}},created(){this.children=[]},mounted(){this.setPlaceholderHeight()},methods:{updateChildren(){this.children.length&&this.children.map((e=>e.updateFromParent()))},async setPlaceholderHeight(){this.fixed&&this.placeholder&&(await uni.$u.sleep(20),this.$uGetRect(".u-tabbar__content").then((({height:e=50})=>{this.placeholderHeight=e})))}}},[["render",function(e,t,a,s,i,c){const m=y,f=b(h("u-safe-bottom"),w);return o(),l(m,{class:"u-tabbar"},{default:r((()=>[p(m,{class:n(["u-tabbar__content",[e.border&&"u-border-top",e.fixed&&"u-tabbar--fixed"]]),ref:"u-tabbar__content",onTouchmove:v(e.noop,["stop","prevent"]),style:u([c.tabbarStyle])},{default:r((()=>[p(m,{class:"u-tabbar__content__item-wrapper"},{default:r((()=>[g(e.$slots,"default",{},void 0,!0)])),_:3}),e.safeAreaInsetBottom?(o(),l(f,{key:0})):d("",!0)])),_:3},8,["onTouchmove","class","style"]),e.placeholder?(o(),l(m,{key:0,class:"u-tabbar__placeholder",style:u({height:i.placeholderHeight+"px"})},null,8,["style"])):d("",!0)])),_:3})}],["__scopeId","data-v-a796ce1c"]]),P=$({__name:"yuni-tabbar",props:["current"],setup(e){const t=_();function a(e){C({url:t.tabBarList[e].pagePath})}return(s,i)=>{const n=I,u=b(h("u-tabbar-item"),D),d=b(h("u-tabbar"),N),c=y;return o(),l(c,null,{default:r((()=>[p(d,{value:e.current?e.current:0,onChange:a,fixed:!0,placeholder:!0,safeAreaInsetBottom:!0},{default:r((()=>[(o(!0),m(f,null,x(S(t).tabBarList,((e,t)=>(o(),l(u,{key:t,text:e.text,name:e.name},{"active-icon":r((()=>[p(n,{class:"icon-style",src:e.selectedIconPath},null,8,["src"])])),"inactive-icon":r((()=>[p(n,{class:"icon-style",src:e.iconPath},null,8,["src"])])),_:2},1032,["text","name"])))),128))])),_:1},8,["value"])])),_:1})}}},[["__scopeId","data-v-825e64c1"]]);export{P as _};
