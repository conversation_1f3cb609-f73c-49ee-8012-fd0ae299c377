/*
graceUI.wxss
link : graceui.hcoder.net
author : <EMAIL> 深海
verson : 1.1.1
*/

/* iconfont */
@font-face {
  font-family: 'grace-iconfont';
  src: url('data:application/x-font-woff;charset=utf-8;base64,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')
    format('woff');
}
.grace-iconfont {
  font-family: 'grace-iconfont' !important;
  font-size: 36upx;
  font-style: normal;
}
.icon-shoppingcard:before {
  content: '\e60a';
}
.icon-share:before {
  content: '\e615';
}
.icon-position:before {
  content: '\e61c';
}
.icon-safe:before {
  content: '\e687';
}
.icon-time2:before {
  content: '\e64c';
}
.icon-home:before {
  content: '\e608';
}
.icon-back:before {
  content: '\e616';
}
.icon-star:before {
  content: '\e645';
}
.icon-like:before {
  content: '\e645';
}
.icon-zan:before {
  content: '\e6ea';
}
.icon-share2:before {
  content: '\e606';
}
.icon-pinglun:before {
  content: '\e6b8';
}
.icon-weibo:before {
  content: '\e6cd';
}
.icon-weixin:before {
  content: '\e63e';
}
.icon-qq:before {
  content: '\e63c';
}
.icon-write:before {
  content: '\e69e';
}
.icon-remove:before {
  content: '\e684';
}
.icon-search:before {
  content: '\e604';
}
.icon-close:before {
  content: '\e602';
}
.icon-close2:before {
  content: '\e78a';
}
.icon-refresh:before {
  content: '\e61a';
}
.icon-loading:before {
  content: '\e9db';
}
.icon-arrow-left:before {
  content: '\e600';
}
.icon-arrow-right:before {
  content: '\e601';
}
.icon-arrow-up:before {
  content: '\e654';
}
.icon-arrow-down:before {
  content: '\e603';
}
.icon-right:before {
  content: '\e60f';
}
.icon-shoucang:before {
  content: '\e605';
}
.icon-time:before {
  content: '\e607';
}
.icon-eye:before {
  content: '\e609';
}
.icon-shaixuan:before {
  content: '\e686';
}
.icon-share3:before {
  content: '\e622';
}
.icon-voice:before {
  content: '\e617';
}
.icon-photograph:before {
  content: '\e60b';
}
.icon-keyboard:before {
  content: '\e627';
}

/* 通用样式 */
view {
  font-size: 28upx;
}
.grace-padding {
  padding: 2%;
  width: 96%;
}
.grace-common-bg {
  background: #f8f8f8;
}
.grace-common-mt {
  margin-top: 15px;
}
.grace-common-border {
  border-top: 1px solid #e9e9e9;
  border-bottom: 1px solid #e9e9e9;
}
.grace-noborder {
  border: 0 !important;
}
@keyframes gradient {
  50% {
    background-position: 100% 0;
  }
}
.grace-gradient-bg {
  width: 100%;
  background: linear-gradient(
    45deg,
    #5a3694 0,
    #13bdce 33%,
    #0094d9 66%,
    #6fc7b5 100%
  );
  background-size: 400%;
  background-position: 0 100%;
  animation: gradient 7.5s ease-in-out infinite;
  padding: 50upx 0;
}
.grace-gradient-bg view {
  color: #fff;
}
/* 通用 css 动画 */
@keyframes grace-fade-in {
  0% {
    opacity: 0.1;
  }
  40% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
.grace-fade-in {
  animation: grace-fade-in 200ms linear;
}

/* 通用组件声明 */
graceSwiper {
  width: 100%;
  height: auto;
}
graceSlider,
graceSpeaker,
graceLoading {
  width: 100%;
}

/* 文字相关 */
.grace-center {
  justify-content: center;
  text-align: center;
}
.grace-h1 {
  font-size: 80upx;
  line-height: 1.8em;
}
.grace-h2 {
  font-size: 60upx;
  line-height: 1.8em;
}
.grace-h3 {
  font-size: 45upx;
  line-height: 1.8em;
}
.grace-h4 {
  font-size: 32upx;
  line-height: 1.8em;
}
.grace-h5 {
  font-size: 30upx;
  line-height: 1.8em;
}
.grace-text {
  font-size: 28upx;
  line-height: 2.2em;
}
.grace-text image {
  width: 100%;
  margin: 20upx 0;
}
.grace-text-small {
  font-size: 24upx;
  line-height: 1.8em;
}
.grace-line-through {
  text-decoration: line-through;
}
.grace-italic {
  font-style: italic;
}
.grace-indent {
  text-indent: 2em;
}
.grace-blod {
  font-weight: 700;
}
.grace-ellipsis {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 栅格布局 */
.grace-flex {
  display: flex !important;
}
.grace-rows {
  display: flex;
  flex-direction: row !important;
}
.grace-columns {
  display: flex;
  flex-direction: column !important;
}
.grace-wrap {
  display: flex;
  flex-wrap: wrap;
}
.grace-nowrap {
  display: flex;
  flex-wrap: nowrap !important;
}
.grace-space-between {
  display: flex;
  justify-content: space-between !important;
}

/* 背景颜色 */
.grace-bg-red {
  background: #f76260;
}
.grace-bg-green {
  background: #00c777;
}
.grace-bg-blue {
  background: #10aeff;
}
.grace-bg-white {
  background: #ffffff;
}

/* 滚动区域 */
.grace-scroll-x {
  width: 100%;
  display: flex;
  white-space: nowrap;
  height: auto;
  font-size: 0;
}
.grace-scroll-x .grace-items {
  width: 355upx;
  height: 200upx;
  vertical-align: top;
  margin: 0 10upx;
  display: inline-flex;
}
.grace-scroll-y {
  width: 100%;
  display: flex;
  font-size: 0;
  height: 200upx;
}
.grace-scroll-y .grace-items {
  width: 100%;
  height: 200upx;
}

/* 数字角标 */
.grace-badge {
  border-radius: 38upx;
  height: 38upx;
  line-height: 38upx;
  padding: 0 13upx;
  font-size: 22upx;
  background: #d1d1d1;
}
.grace-badge-red {
  background: #f76260;
  color: #fff !important;
}
.grace-badge-green {
  background: #00c777;
  color: #fff !important;
}
.grace-badge-blue {
  background: #10aeff;
  color: #fff !important;
}
.grace-badge-yellow {
  background: #f0ad4e;
  color: #fff !important;
}

/* 普通列表 */
.grace-list {
  width: 100%;
  border-top: 1px solid #e9e9e9;
  border-bottom: 1px solid #e9e9e9;
  background: #fff;
  padding: 5px 0;
}
.grace-list .items {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  position: relative;
}
.grace-list .items .icons {
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  margin: 18px 0 15px 15px;
  flex-shrink: 0;
  overflow: hidden;
  font-size: 0;
}
.grace-list .items .icons image {
  width: 20px;
  height: 20px;
}
.grace-list .items .title {
  width: 100%;
  margin-left: 18px;
  padding: 18px 30px 18px 0;
  font-size: 16px;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  border-bottom: 1px solid #e9e9e9;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.grace-list .title text {
  font-size: 13px;
  color: #b2b2b2;
  margin-left: 10px;
  float: right;
}
.grace-list > .items:last-child .title {
  border: none !important;
}
.grace-list .items .arrow-right {
  font-family: 'grace-iconfont' !important;
  font-size: 36rpx;
  font-style: normal;
  height: 56px;
  line-height: 56px;
  text-align: center;
  width: 30px;
  position: absolute;
  z-index: 1;
  right: 0;
  top: 0;
}
.grace-list .items .arrow-right:before {
  content: '\e601';
  color: #b2b2b2;
}
.grace-list .items .icon-r {
  position: absolute;
  z-index: 1;
  right: 35px;
  top: 0px;
}

/* 多格图标 */
.grace-boxes {
  width: 23%;
  margin: 0 1%;
  padding: 10upx 0;
}
.grace-boxes-img {
  width: 70%;
  margin: 0 auto;
  text-align: center;
  padding-bottom: 10upx;
  font-size: 0;
}
.grace-boxes-img image {
  width: 100%;
}
.grace-boxes-text {
  line-height: 2em;
  text-align: center;
  font-size: 22upx;
}

/* 图文列表 */
.grace-imgitems {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
}
.grace-imgitems .grace-items {
  width: 48%;
  margin: 10upx 1%;
  overflow: hidden;
  font-size: 0;
  position: relative;
}
.grace-imgitems image {
  width: 100%;
}
.grace-imgitems-text {
  margin: 6upx 1%;
  width: 98%;
  font-size: 26upx;
  height: 50upx;
  line-height: 50upx;
}
.grace-imgitems-more {
  display: flex;
  justify-content: space-between;
  line-height: 46upx;
}
.grace-imgitems-button {
  width: auto;
  border: 1px solid #00c777;
  line-height: 44upx;
  padding: 0 20upx;
  color: #49a761;
  font-size: 24upx;
  border-radius: 5upx;
}
.grace-imgitems-tips {
  display: flex;
  flex-wrap: nowrap;
  width: auto;
  position: absolute;
  top: 10upx;
  color: #ffffff;
  height: 36upx;
  font-size: 20upx;
  line-height: 36upx;
  background: #f76260;
  padding: 0 6px;
}
.grace-imgitems-tips-green {
  background: #00c777;
}
.grace-imgitems-tips-r {
  right: 0;
}

/* 新闻列表 */
.grace-news-list {
  padding: 12upx 0;
}
.grace-news-list > navigator {
  display: block;
  width: 100%;
  padding: 10px 0;
  margin: 12upx 0;
}
.grace-news-list-items {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  position: relative;
}
.grace-news-list-img {
  width: 200upx;
  flex-shrink: 0;
}
.grace-news-list-title {
  width: 100%;
  overflow: hidden;
}
.grace-news-list-title-main {
  line-height: 1.5em;
  font-size: 32upx;
  width: 100%;
}
.grace-news-list-title-desc {
  font-size: 24upx;
  display: block;
  color: #666;
  margin-top: 12upx;
  height: 40upx;
  line-height: 40upx;
}
.grace-news-tips {
  width: auto;
  padding: 0 12upx;
  border-radius: 5upx;
  overflow: hidden;
  background: #ff4343;
  position: absolute;
  top: 0;
  color: #ffffff;
  height: 36upx;
  line-height: 36upx;
  font-size: 20upx;
}
.grace-news-tips-l {
  left: 0;
}
.grace-news-tips-r {
  right: 0;
}
.grace-news-list-info {
  width: 100%;
  margin-top: 10upx;
  line-height: 36upx;
  font-size: 24upx;
  color: #666;
  justify-content: space-between;
}
.grace-news-list-info view {
  font-size: 24upx;
  color: #666;
}
.grace-news-list-info text {
  font-size: 24upx;
  color: #666;
  margin-right: 10upx;
}
.grace-news-list-img-news {
  width: 100%;
  flex-wrap: wrap;
}
.grace-news-list-imgs {
  justify-content: space-between;
  width: 100%;
  display: flex;
  margin: 18upx 0;
  align-items: flex-start;
}
.grace-news-list-imgs image {
  width: 31.3%;
  margin: 0 1%;
}
.grace-news-list-img-big {
  width: 100%;
  padding: 18upx 0;
}
.grace-news-list-img-big image {
  width: 100%;
}
.grace-list-imgs-l {
  margin-right: 10px;
}
.grace-list-imgs-r {
  margin-left: 10px;
}

/* 选项卡 */
.grace-tab {
  padding: 0;
}
.grace-tab-title {
  white-space: nowrap;
  text-align: center;
  background: #ffffff;
}
.grace-tab-title view {
  width: auto;
  padding: 0 12px;
  margin: 0 8px;
  line-height: 42px;
  display: inline-block;
  text-align: center;
  border-bottom: 2px solid #ffffff;
  font-size: 30upx;
}
.grace-tab-title view:first-child {
  margin-left: 0;
}
.grace-tab-title view:last-child {
  margin-right: 0;
}
.grace-tab-titleNew {
  white-space: nowrap;
  text-align: center;
  background: #ffffff;
}
.grace-tab-titleNew view {
  width: auto;
  padding: 0 12px;
  margin: 0 8px;
  line-height: 30px;
  display: inline-block;
  text-align: center;
  border: 1upx solid #0079fe;
  font-size: 30upx;
}
.grace-tab-titleNew view:first-child {
  margin-left: 0;
}
.grace-tab-titleNew view:last-child {
  margin-right: 0;
}
.grace-tab-current {
  border-bottom: 4upx solid #00c777 !important;
  color: #00c777;
}
.grace-tab-currentNew {
  border-bottom: 4upx solid #0079fe !important;
  color: #0079fe;
}
.grace-tab-currentAll {
  background: #0079fe;
  color: #fff;
}
.grace-tab-swiper {
  width: 100%;
  height: 350upx;
  padding: 0;
}
.grace-tab-swiper swiper-item {
  width: 100%;
}
.grace-tab-swiper swiper-item navigator {
  line-height: 70upx;
  width: 100%;
  display: block;
}
.grace-tab-swiper-full {
  width: 100%;
  height: auto;
}
.grace-tab-swiper-full swiper-item {
  width: 100%;
}
.grace-tab-swiper-full scroll-view {
  width: 100%;
  height: 100%;
}

/* 折叠面板 */
.grace-accordion {
  background: #ffffff;
}
.grace-accordion-items {
  overflow: hidden;
  border-bottom: 1px solid #f2f3f4;
}
.grace-accordion-items:last-child {
  border: none;
}
.grace-accordion-title {
  display: flex;
  flex-wrap: nowrap;
  width: 92%;
  justify-content: space-between;
  padding: 0 4% 0 4%;
  line-height: 88upx;
  height: 88upx;
  overflow: hidden;
  font-size: 32upx;
}
.grace-accordion .grace-current {
  background: #f2f3f4;
}
.grace-accordion-body {
  overflow: hidden;
}

/* 标题及更多 */
.grace-title {
  padding: 10upx 0;
  line-height: 1.8em;
}
.grace-title .grace-text-small {
  color: #888;
}
.grace-more-bottom {
  font-size: 24upx;
  text-align: center;
  justify-content: center;
  padding: 15upx 0;
  line-height: 40upx;
}
.grace-more-bottom text {
  font-size: 24upx;
}
.grace-more-r {
  display: block;
  width: auto;
  flex-shrink: 0;
  font-size: 22upx;
}
.grace-more-r text {
  font-size: 22upx;
}

/* 横向广告组 */
.grace-box-banner {
  padding: 20upx 0;
  display: flex;
  flex-wrap: nowrap;
  overflow: hidden;
  background: #fff;
}
.grace-box-banner .garce-items {
  width: 25%;
  border-right: 1px solid #f1f2f3;
  justify-content: center;
  line-height: 1.4em;
  text-align: center；;
}
.grace-box-banner .garce-items:last-child {
  border: none;
}
.grace-box-banner .garce-items view {
  justify-content: center;
  text-align: center;
}
.grace-box-banner .garce-items text text {
  font-size: 22rupx;
  color: #666;
}
.grace-box-banner .line1 {
  font-size: 36upx;
  line-height: 60upx;
  overflow: hidden;
}
.grace-box-banner .line1 text {
  font-size: 26upx;
  color: #666;
  line-height: 65upx;
  margin-left: 5upx;
}
.grace-box-banner .line2 {
  font-size: 26upx;
  color: #666;
  line-height: 32upx;
}

/* 可选标签 */
.grace-select-tips {
  padding: 10upx 0;
}
.grace-select-tips checkbox-group,
.grace-select-tips radio-group {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.grace-select-tips label {
  display: block;
  width: auto;
  overflow: hidden;
  padding: 15upx 22upx;
  height: 30upx;
  line-height: 30upx;
  margin: 6px;
  background: #f6f7f8;
  font-size: 26upx;
  border-radius: 5upx;
}
.grace-select-tips label checkbox,
.grace-select-tips label radio {
  display: none;
}
.grace-checked {
  background: #00c777 !important;
  color: #ffffff;
}

/* 底部导航 */
.grace-footer {
  display: flex;
  position: fixed;
  z-index: 2;
  left: 0;
  bottom: 0;
  background: #ffffff;
  width: 100%;
  height: 90upx;
  flex-wrap: nowrap;
  overflow: hidden;
  box-shadow: 1px 1px 6px #888;
}
.grace-footer .grace-input {
  width: 100%;
  margin: 15upx 20upx;
  padding: 0 15upx;
  background: #f4f5f6;
  height: 60upx;
  border-radius: 60upx;
  flex-wrap: nowrap;
  display: flex;
}
.grace-input-icon {
  width: 60upx;
  height: 60upx;
  line-height: 60upx;
  font-size: 30upx;
  color: #000000;
  justify-content: center;
  flex-shrink: 0;
}
.grace-footer .grace-input input {
  width: 100%;
  padding: 10upx 15upx;
  height: 40upx;
  line-height: 40upx;
  font-size: 26upx;
  background: none;
  border: 0;
}
.grace-footer .icons {
  width: 70upx;
  height: 60upx;
  margin: 15upx 8upx;
  text-align: center;
  line-height: 60upx;
  flex-shrink: 0;
  font-size: 50upx;
}
.grace-footer .grace-items {
  width: auto;
  line-height: 90upx;
  flex-shrink: 0;
}
.grace-footer button {
  width: 100%;
  border: 0;
  border-radius: 0;
  height: 90upx;
  line-height: 90upx;
}

/* 表单 */
.grace-form {
  padding: 0;
  width: 100%;
}
.grace-form form {
  display: block;
  width: 100%;
  overflow: hidden;
}
.grace-form .grace-items {
  display: flex;
  border-bottom: 1px solid #f2f3f4;
  width: 100%;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 10upx 0;
}
.grace-form .grace-items .grace-label {
  width: 130upx;
  height: 80upx;
  line-height: 80upx;
  flex-shrink: 0;
  overflow: hidden;
}
.grace-form .grace-items .input {
  width: 98%;
  height: 40upx;
  line-height: 40upx;
  background: none;
  flex-shrink: 1;
  border: 0;
  text-align: right;
  margin: 10px;
  margin-left: 40upx;
}
.grace-form-r {
  width: 100%;
  padding: 0 10upx;
  line-height: 80upx;
  display: block;
  overflow: hidden;
  flex-shrink: 1;
  margin-left: 40upx;
  text-align: right;
}
.grace-form picker {
  width: 100%;
  height: 80upx;
  line-height: 80upx;
  background: none;
  border: 0;
  text-align: right;
}
.grace-form switch {
  margin-top: 10upx;
}
.grace-form .grace-items picker text {
  justify-content: right;
  line-height: 80upx;
  font-size: 28upx;
}
.grace-form .grace-items picker text:after {
  content: '\e601';
  padding-left: 10upx;
  color: #888;
  font-family: 'grace-iconfont' !important;
  font-size: 30upx;
}
.grace-form textarea {
  width: 98%;
  height: 100upx;
  line-height: 2em;
  background: none;
  border: 0;
  padding: 8upx 2%;
  font-size: 28upx;
}
.grace-label-x {
  width: 100%;
  padding: 12upx 0;
  flex-shrink: 1;
  margin-left: 40upx;
}
.grace-label-x label {
  margin: 10upx;
  font-size: 28upx;
}
.grace-label-y {
  width: 100%;
  padding: 12upx 0;
  flex-shrink: 1;
  margin-left: 40upx;
}
.grace-form radio-group,
.grace-form checkbox-group {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.grace-label-y label {
  margin: 8px 0;
  font-size: 28upx;
  width: 100%;
}
.grace-items-wbg {
  background: #fff;
  border: 0 !important;
  border-radius: 8upx;
  padding: 3upx 0 !important;
}
.grace-items-wbg input {
  text-align: left !important;
}
.grace-login-three {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
}
.grace-login-three view {
  width: 44px;
  height: 44px;
  line-height: 44px;
  font-size: 36px;
  color: #fff;
  text-align: center;
  margin: 8px;
}

/* 遮罩层 */
.grace-mask {
  background: rgba(0, 0, 0, 0.6);
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 1;
}

/* steps */
.grace-steps {
  padding: 20upx 0;
  background: #fff;
  display: flex;
  flex-wrap: nowrap;
}
.grace-steps view {
  display: flex;
  width: 100%;
}
.grace-steps .step {
  width: 100%;
  margin: 0 5px;
  display: flex;
  flex-wrap: nowrap;
}
.grace-steps .step:last-child {
  display: inline-flex;
  flex-shrink: 0;
  width: auto;
}
.grace-steps .step-circle {
  width: 50upx;
  height: 50upx;
  border-radius: 50upx;
  background: #f1f1f3;
  justify-content: center;
  line-height: 50upx;
  flex-shrink: 0;
  margin-right: 15upx;
  color: #666;
  font-size: 28upx;
}
.grace-steps .step-content {
  width: 100%;
  height: 22upx;
  border-bottom: 1px solid #f1f1f3;
}
.grace-steps .step-title {
  line-height: 50upx;
  height: 50upx;
  background: #ffffff;
  width: auto;
  padding-right: 12upx;
}
.grace-steps .current .step-circle {
  background: #00b26a;
  color: #ffffff;
}
.grace-steps .current .step-content {
  border-color: #00b26a;
}
.grace-steps .current .step-title {
  color: #00b26a;
}

/* 评论列表 */
.grace-comment {
  padding: 5upx 0;
}
.grace-comment-list {
  display: flex;
  flex-wrap: nowrap;
  padding: 10upx 0;
  margin: 10upx 0;
}
.grace-comment-face {
  width: 70upx;
  height: 70upx;
  border-radius: 100%;
  margin-right: 20upx;
  flex-shrink: 0;
  overflow: hidden;
}
.grace-comment-face image {
  width: 100%;
  border-radius: 100%;
}
.grace-comment-body {
  width: 100%;
}
.grace-comment-top {
  display: flex;
  flex-wrap: nowrap;
  line-height: 1.5em;
  justify-content: space-between;
}
.grace-comment-top text {
  color: #0a98d5;
  font-size: 24upx;
}
.grace-comment-top text:last-child {
  color: #666666;
}
.grace-comment-date {
  display: flex;
  flex-wrap: nowrap;
  line-height: 1.5em;
  justify-content: space-between;
}
.grace-comment-date text {
  color: #666666;
  font-size: 24upx;
}
.grace-comment-content {
  line-height: 1.6em;
  font-size: 28upx;
  padding: 8upx 0;
}
.grace-comment-zan {
  color: #0a98d5 !important;
}
.grace-comment-replay-btn {
  background: #f4f5f6;
  font-size: 24upx;
  padding: 8upx 15upx;
  border-radius: 30upx;
  color: #333 !important;
  margin: 0 10upx;
}
.grace-comment-imgs {
  display: flex;
  padding: 8upx 0;
  flex-wrap: wrap;
}
.grace-comment-imgs .imgs {
  width: 31.3% !important;
  max-height: 90px;
  margin: 5px 1%;
  overflow: hidden;
}
.grace-comment-imgs .imgs image {
  width: 100%;
}

/* 搜索 */
.grace-search {
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-wrap: nowrap;
}
.grace-search-btns {
  width: 50upx;
  height: 50upx;
  margin: 8upx 8px;
}
.grace-search-btns image {
  width: 50upx;
  height: 50upx;
}
.grace-search-in {
  background: #ffffff;
  border-radius: 66upx;
  height: 36upx;
  overflow: hidden;
  display: flex;
  flex-wrap: nowrap;
  width: 92%;
  padding: 15upx 4%;
}
.grace-search-icon {
  width: 36upx;
  height: 36upx;
  line-height: 36upx;
  color: #007aff;
  justify-content: center;
  font-family: 'grace-iconfont' !important;
  font-size: 30upx;
  font-style: normal;
  flex-shrink: 0;
}
.grace-search-icon:before {
  content: '\e604';
  color: #007aff;
}
.grace-search input {
  width: 100%;
  background: #900;
  padding: 0 20upx;
  border: 0;
  background: #fff;
  height: 28upx;
  line-height: 28upx;
  margin: 0;
  color: #000;
}
.grace-search-clear:before {
  content: '\e78a' !important;
  color: #cccccc !important;
}
.grace-search-remove:before {
  font-family: 'grace-iconfont';
  font-style: normal;
  content: '\e684' !important;
  color: #cccccc !important;
  font-size: 40upx;
  line-height: 40upx;
}

/* 标签 */
.grace-tips {
  padding: 10upx 0;
  display: flex;
  flex-wrap: wrap;
}
.grace-tips view {
  padding: 0 20upx;
  border-radius: 30upx;
  margin-right: 15upx;
  margin-bottom: 15upx;
  font-size: 24upx;
  line-height: 46upx;
  border: 1px solid #d1d1d1;
  color: #666666;
  width: auto;
}

/* 文件添加及预览 */
.grace-add-file {
  padding: 20upx;
  display: flex;
  flex-wrap: wrap;
  width: 710upx;
}
.grace-add-btn {
  width: 213upx;
  margin: 10upx;
  background: #f5f5f5;
  padding: 45upx 0;
}
.grace-add-btn view {
  font-size: 26upx;
  height: 40upx;
  line-height: 40upx;
  text-align: center;
  color: #999999;
  width: 100%;
}
.grace-add-btn view:first-child {
  font-size: 80upx;
  height: 80upx;
  line-height: 80upx;
}
.grace-add-file .garce-items {
  width: 213upx;
  height: 213upx;
  margin: 10upx;
  overflow: hidden;
  position: relative;
}
.grace-add-file .garce-items image {
  width: 100%;
}
.grace-add-file .garce-items-media {
  width: 213upx;
  height: 213upx;
  margin: 10upx;
  overflow: hidden;
}
.grace-add-file .garce-items-media video {
  width: 213px;
  height: 190upx;
}
.grace-add-file .grace-remove {
  height: 40upx;
  font-size: 22upx;
  justify-content: center;
  line-height: 60upx;
  overflow: hidden;
}
.grace-add-file .garce-items .grace-close {
  font-family: 'grace-iconfont' !important;
  width: 46upx;
  height: 46upx;
  position: absolute;
  z-index: 1;
  right: 10upx;
  bottom: 10upx;
  font-size: 46upx;
  color: #ff0000;
  opacity: 0.8;
}
.grace-add-file .garce-items .grace-close:before {
  content: '\e602';
}

/* 普通表格 */
.grace-stable {
  padding: 0;
}
.grace-stable .title {
  flex-wrap: nowrap;
  display: flex;
}
.grace-stable .title > view {
  line-height: 50px;
  width: 25%;
  text-align: center;
  font-weight: 700;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-bottom: 1px solid #d1d1d1;
  border-right: 1px solid #d1d1d1;
  background: #f1f2f3;
}
.grace-stable .title > view:last-child {
  border-right: none;
}
.grace-stable .body {
  flex-wrap: nowrap;
  display: flex;
}
.grace-stable .body > view {
  line-height: 46px;
  width: 25%;
  text-align: center;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border-bottom: 1px solid #d1d1d1;
  font-size: 24upx;
  border-right: 1px solid #d1d1d1;
}
.grace-stable .body > view:last-child {
  border-right: none;
}

/* 数据表格 */
.grace-table {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  width: 100%;
}
.grace-table-left {
  width: 200upx;
}
.grace-table-right {
  width: 539upx;
}
.grace-table-title {
  width: 100%;
  display: flex;
  line-height: 100upx;
  background: #ffffff;
  justify-content: center;
  flex-wrap: nowrap;
  margin-top: 2upx;
}
.grace-table-title view {
  line-height: 100upx;
  font-size: 30upx;
  text-align: center;
  width: 100%;
}
.grace-table-right scroll-view {
  width: 100%;
}
.grace-table-right .rows {
  width: 800upx;
  margin-top: 2upx;
  background: #ffffff;
  display: flex;
  flex-wrap: nowrap;
}
.grace-table-right .rows .items {
  width: 25%;
  line-height: 100upx;
  text-align: center;
  overflow: hidden;
}

/* 时间轴 */
.grace-timeline {
  width: 100%;
}
.grace-timeline .rows {
  padding: 20upx;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  position: relative;
}
.grace-timeline-time {
  width: 130upx;
  flex-shrink: 0;
  overflow: hidden;
}
.grace-timeline-time view {
  font-size: 22upx;
  color: #ccc;
  text-align: right;
  overflow: hidden;
  line-height: 1.5em;
}
.grace-timeline-time view:last-child {
  font-size: 32upx;
  color: #46a4da;
}
.grace-timeline-tips {
  width: 70upx;
  height: 100%;
  margin: 0 15upx;
  flex-shrink: 0;
  position: relative;
}
.grace-timeline-circular {
  width: 50upx;
  height: 50upx;
  border: 10upx solid #afdcf8;
  border-radius: 100%;
  line-height: 50upx;
  text-align: center;
  background: #46a4da;
  color: #fff;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
}
.grace-timeline-circular image {
  border-radius: 100%;
  font-size: 0;
  width: 100%;
}
.grace-timeline-content {
  width: 100%;
  background: #46a4da;
  color: #fff;
  padding: 18upx;
  border-radius: 12upx;
}
.grace-timeline-content view {
  color: #fff;
}
.grace-timeline-line {
  width: 8upx;
  height: 100%;
  background: #afdcf8;
  position: absolute;
  z-index: 1;
  left: 195upx;
  top: 30upx;
}

/* 全屏分类 */
.grace-cate {
  width: 100%;
  height: 100%;
  background: #ffffff;
  position: fixed;
  z-index: 99;
  left: 0;
  top: 0;
  display: flex;
  flex-wrap: wrap;
}
.grace-cate-left {
  width: 25%;
  height: 100%;
  display: flex;
  background: #f8f8f8;
}
.grace-cate-left view {
  height: 150upx;
  line-height: 150upx;
  font-size: 30upx;
  border-bottom: 1px solid #eee;
  text-align: center;
}
.grace-cate-left .current {
  background: #ffffff !important;
}
.grace-cate-right {
  width: 75%;
  height: 100%;
  display: flex;
  background: #ffffff;
}
.grace-cate-sons {
  padding: 15upx 3%;
  width: 94%;
}
.grace-cate-sons-title {
  line-height: 80upx;
  margin-top: 18upx;
  font-size: 30upx;
}
.grace-cate-sons-nav {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px dashed #d1d1d1;
  padding-bottom: 22upx;
}
.grace-cate-sons-nav view {
  width: 29.3%;
  padding: 15upx 0;
  margin: 8upx 2%;
  color: #888;
  overflow: hidden;
}

/* 购物车 */
.grace-shoppingcard {
  background: #ffffff;
  width: 94%;
  padding: 15upx 3%;
  margin-bottom: 22upx;
}
.grace-shoppingcard .shop-name {
  line-height: 50upx;
}
.grace-shoppingcard .goods {
  margin: 10upx 0;
  display: flex;
  flex-wrap: nowrap;
}
.grace-shoppingcard .goods image {
  width: 150upx;
  margin-right: 20upx;
  flex-shrink: 0;
}
.grace-shoppingcard .goods .desc {
  width: 100%;
}
.grace-shoppingcard .goods .goods-title {
  line-height: 1.4em;
}
.grace-shoppingcard .goods .goods-price {
  margin-top: 8upx;
  color: #f00;
  font-size: 36upx;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.grace-shoppingcard .goods .goods-number {
  padding: 2px 0;
}
.grace-shoppingcard .goods-remove {
  display: flex;
  line-height: 50upx;
  margin-top: 30upx;
  color: #cccccc;
  font-size: 26upx;
  justify-content: flex-end;
}
.grace-shoppingcard .goods-remove text {
  color: #cccccc;
  margin-right: 20upx;
}

/* filter */
.grace-filter {
  width: 100%;
  background: #ffffff;
  position: fixed;
  z-index: 9;
  left: 0;
  top: 0;
  border-bottom: 1px solid #f2f3f4;
  display: flex;
  flex-wrap: nowrap;
}
.grace-filter .items {
  display: flex;
  flex-wrap: nowrap;
  width: 25%;
  justify-content: center;
  height: 90upx;
  line-height: 90upx;
}
.grace-filter .items image {
  width: 40upx;
  margin: 22upx 10upx;
}
.grace-filter .items text {
  margin-left: 10upx;
  font-size: 24upx;
}
.grace-filter-options {
  width: 100%;
  position: absolute;
  z-index: 10;
  padding: 20upx 0;
  right: 0;
  top: 92upx;
  background: #ffffff;
}
.grace-filter-options .option {
  display: flex;
  justify-content: space-between;
  text-indent: 2em;
  height: 70upx;
  line-height: 70upx;
}
.grace-filter-options .option text {
  margin-right: 30upx;
  font-size: 30upx;
  color: #f00;
  font-weight: 700;
}
.grace-filter-options .current {
  color: #f00;
  font-weight: 700;
}
.grace-filter-buttons {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  position: absolute;
  z-index: 11;
  left: 0;
  bottom: 0;
  height: 90upx;
  background: #fff;
}
.grace-filter-buttons view {
  width: 50%;
  height: 100upx;
  line-height: 100upx;
  text-align: center;
  position: relative;
}
.grace-filter-buttons view:last-child {
  background: #ff0000;
  color: #fff;
}
.grace-filter-buttons view button {
  opacity: 0;
  width: 100%;
  position: absolute;
  z-index: 9;
  left: 0;
  top: 0;
  height: 90upx;
}

/* 瀑布流 */
.grace-waterfall {
  width: 100%;
  display: flex;
  flex-wrap: no-wrap;
}
.grace-waterfall .list {
  width: 46%;
  margin: 0 2%;
}
.grace-waterfall .items {
  margin: 20upx 0;
  position: relative;
}
.grace-waterfall .items .imgs {
  width: 100%;
}
.grace-waterfall .items .title {
  line-height: 1.4em;
  font-size: 16px;
  margin-top: 12px;
}
.grace-waterfall .items .price {
  padding: 10upx 0;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  font-size: 32upx;
  line-height: 35px;
  color: #ff0036;
}
.grace-waterfall .items .tips {
  height: 32px;
  width: auto;
  line-height: 32px;
  background: #ff0036;
  color: #ffffff;
  padding: 0 20upx;
  border-radius: 3px;
}

/* 滚动操作 */
.grace-scroll-do {
  width: 720upx;
  padding-left: 30upx;
  border-bottom: 1px solid #e6e6e6;
  border-top: 1px solid #e6e6e6;
}
.grace-scroll-do scroll-view {
  border-bottom: 1px solid #e6e6e6;
  width: 720upx;
}
.grace-scroll-do scroll-view:last-child {
  border: none;
}
.grace-scroll-do .grace-items {
  width: 720upx;
  flex-wrap: nowrap;
  height: 80upx;
  margin: 20upx 0;
  align-items: flex-start;
}
.grace-scroll-do .grace-items image {
  width: 100upx;
  flex-shrink: 0;
  margin-right: 20upx;
  font-size: 0;
  border-radius: 3upx;
}
.grace-scroll-do .grace-items .contents {
  width: 100%;
  margin-right: 20upx;
}
.grace-scroll-do .grace-items .contents view {
  line-height: 1.6em !important;
}
.grace-scroll-do .btn {
  background: #ff3a30;
  color: #fff;
  width: 130upx;
  height: 140upx;
  line-height: 140upx;
  justify-content: center;
  text-align: center;
  margin: 0;
  overflow: hidden;
}
.grace-scroll-do .btn-first {
  background: #5959d3;
}

/* 商品展示 */
.grace-product-title {
  background: #fff;
  font-weight: 600;
  line-height: 1.8;
  font-size: 32upx;
}
.grace-product-share {
  width: 28px;
  font-size: 24px;
  color: #ff7900;
  line-height: 40px;
  flex-shrink: 0;
  margin-left: 12px;
}
.grace-product-price {
  background: #fff;
  padding: 20upx 2%;
  padding-bottom: 5upx;
  color: #ff7900;
  line-height: 30px;
  font-size: 24px;
  font-weight: 600;
  margin-top: 5px;
}
.grace-product-price text {
  color: #999;
  font-size: 16px;
  text-decoration: line-through;
  line-height: 30px;
  margin-left: 8px;
}
.grace-product-desc {
  background: #fff;
  padding: 8upx 2%;
  padding-bottom: 20upx;
  font-size: 24upx;
  color: #666666;
  line-height: 1.8;
  justify-content: space-between;
  display: flex;
  flex-wrap: nowrap;
}
.grace-product-menu {
  padding: 0upx 2%;
  padding-top: 10px;
  background: #fff;
  margin-top: 5px;
  display: flex;
  flex-wrap: nowrap;
}
.grace-product-menu view {
  width: 40%;
  margin: 0 5%;
  line-height: 80upx;
  border-bottom: 6upx solid #ffffff;
  font-size: 32upx;
  text-align: center;
}
.grace-product-menu .active {
  border-color: #ff7900;
  font-weight: 600;
  color: #ff7900;
}
.grace-product-info {
  background: #fff;
  padding: 30upx 2%;
}
.grace-product-info image {
  width: 100%;
}
.grace-product-btn {
  width: 30%;
  height: 90upx;
  line-height: 90upx;
  font-size: 30upx;
  color: #fff;
  text-align: center;
  background: #ff7900;
}
.grace-product-attr {
  width: 94%;
  padding: 5px 3%;
  height: 80%;
  position: absolute;
  left: 0;
  bottom: 0;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  background: #fff;
}
.grace-product-attr-info {
  height: 60px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f1f1f3;
  padding-bottom: 12px;
}
.grace-product-attr-info image {
  width: 60px;
  height: auto;
  margin-right: 10px;
  flex-shrink: 0;
}
.grace-product-attr-info .title {
  width: 100%;
}
.grace-product-attr-info .title text {
  font-size: 10px;
  color: #666666;
}
.grace-product-attr-list {
  padding-top: 15px;
  border-bottom: 1px solid #f1f1f3;
}
.grace-product-attr-list .title {
  font-weight: 700;
}
.grace-product-attr-btn {
  width: 94%;
  padding: 5px 3%;
  position: fixed;
  z-index: 10;
  bottom: 0;
  left: 0;
}
.grace-product-attr-btn button {
  height: 40px;
  line-height: 40px;
}
.grace-product-attr-close {
  padding: 5px 0;
  line-height: 25px;
  text-align: right;
}
.grace-product-attr-close text {
  font-size: 24px;
  color: #666666;
}

/* 文章骨架结构 */
.grace-skeleton {
  padding: 5px 0;
  background: #f1f2f3;
  border-radius: 8px;
}
.grace-article-title {
  margin: 8px 12px;
  font-size: 26px;
  line-height: 1.5em;
  font-weight: 700;
}
.grace-article-author-line {
  margin: 3px 12px;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.grace-article-author {
  display: flex;
  flex-wrap: nowrap;
}
.grace-article-author image {
  width: 28px;
  height: 28px;
  border-radius: 100%;
}
.grace-article-author .author-name {
  line-height: 28px;
  padding-left: 5px;
}
.grace-article-author-line .btn {
  display: inline-block;
  height: 28px;
  line-height: 28px;
  border-radius: 3px;
  padding: 0 10px;
  background: #00b26a;
  color: #ffffff;
}
.grace-article-info-line {
  margin: 8px 12px;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
}
.grace-article-info-line view {
  color: #888;
  line-height: 20px;
  font-size: 12px;
}
.grace-article-contents {
  margin: 10px 0;
}
.grace-article-contents .img-item {
  width: 100%;
}
.grace-article-contents .img-item image {
  width: 100%;
}
.grace-article-contents .text-item {
  margin: 8px 12px;
  line-height: 2.2em;
  font-size: 16px;
  color: #2f2f2f;
}

/* 分段器 */
