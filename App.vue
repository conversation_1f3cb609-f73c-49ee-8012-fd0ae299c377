<script setup>
import { ref } from 'vue'

import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useTokenStore } from '@/store/token.js'
import { useMainStore } from '@/store/index.js'
import { useUserStore } from '@/store/user.js'
const userStore = useUserStore()
import { dingLogin, getUserInfo } from '@/common/api.js'
import { config } from '@/config/config.js'
// #ifdef APP-PLUS
import { getTrarenInfo } from '@/common/api'
import permision from '@/common/app-plus/permission.js'
import kvStore from '@/common/store/uniKVStore'
import { appPackage } from '@/package.json'
import { systemUpdate } from '@/common/api'

import { cerSha1, iosMd5 } from '@/common/confound.js'
import checkAPPVersion1 from '@/common/appUpdate/appUpdate.js'
import updateAppDemo from '@/common/appUpdate/appUpdate_demo.js'
import jpushInit from '@/common/jpush/index.js'
// #endif
// #ifdef H5 || H5-DINGTALK
import VConsole from 'vconsole'
// #endif
// #ifdef H5-DINGTALK

// #endif
// 状态store
import * as dingtalk from 'dingtalk-jsapi'
import Platform from '@/common/platform/ePlatform'
const tokenStore = useTokenStore()
const mainStore = useMainStore()

onLaunch(() => {
  console.log('App Launch')
  // #ifdef APP-PLUS
  setTimeout(() => {
    plus.navigator.closeSplashscreen()
  }, 2000)
  // 签名证书校验
  checkCerSha()
  // #endif

  initConfig()
  checkLogin()
})

// #ifdef APP-PLUS || MP-WEIXIN
networkType()
// 判断、监听网络类型
function networkType() {
  // 获取网络状态
  uni.getNetworkType({
    success: function (res) {
      console.log('当前网络类型', res.networkType)
      if (
        res.networkType != 'wifi' &&
        res.networkType != '5g' &&
        res.networkType != '4g'
      ) {
        uni.showModal({
          content: '当前网络信号弱，请检查网络设置',
          confirmText: '确定',
          showCancel: false,
          success: function (res) {},
        })
      }
    },
  })

  // 监听网络状态变化
  uni.onNetworkStatusChange(function (res) {
    console.log('当前网络是否连接及网络类型', res.isConnected, res.networkType)
    if (!res.isConnected) {
      uni.showModal({
        content: '当前网络信号弱，请检查网络设置',
        confirmText: '确定',<uni-view data-v-bd397bb3 class="parkingList-list-item">
        showCancel: false,
        success: function (res) {},
      })
    }
  })
}
// #endif

// #ifdef H5-DINGTALK
dingtalkTitle()
function dingtalkTitle() {
  if (dingtalk.env.platform !== 'notInDingTalk') {
    let style = document.createElement('style')
    style.type = 'text/css'
    style.innerHTML = 'uni-page-head,.uni-page-head{display:none;}'
    document.getElementsByTagName('head').item(0).appendChild(style)
  }
}
// #endif
// #ifdef APP-PLUS
// 摄像头权限获取
async function checkPermission() {
  let status = permision.isIOS
    ? await permision.requestIOS('camera')
    : await permision.requestAndroid('android.permission.CAMERA')
  if (status === null || status === 1) {
    status = 1
  } else {
    uni.showModal({
      content: '需要相机权限',
      confirmText: '设置',
      success: function (res) {
        if (res.confirm) {
          permision.gotoAppSetting()
        }
      },
    })
  }
  return status
}
// 麦克风权限获取
async function checkMicrophonePermission() {
  let status = permision.isIOS
    ? await permision.requestIOS('record')
    : await permision.requestAndroid('android.permission.RECORD_AUDIO')
  if (status === null || status === 1) {
    status = 1
  } else if (status === 2) {
    uni.showModal({
      content: '系统麦克风已关闭',
      confirmText: '确定',
      showCancel: false,
      success: function (res) {},
    })
  } else {
    uni.showModal({
      content: '需要麦克风权限',
      confirmText: '设置',
      success: function (res) {
        if (res.confirm) {
          permision.gotoAppSetting()
        }
      },
    })
  }
  return status
}
// 获取租户信息
function lessee() {
  if (config.isText_tenantId) {
    var tenantName = tokenStore.tenantName
    console.log('tenantName', tenantName)
    uni.showModal({
      title: '租户名称',
      editable: true,
      placeholderText: tenantName ? tenantName : '请输入租户名称',
      confirmText: '确认',
      cancelText: '取消', // 取消按钮的文字
      success(res) {
        // 更新store中的tenantName
        tokenStore.tenantName = res.content
        if (res.confirm) {
          // 向登录页传递租户名称，进行表单展示
          uni.$emit('updateTenantName', { tenantName: res.content })
          // 获取对应租户id
          getTrarenInfo({
            tenantLoginName: res.content ? res.content : 'system',
          }).then((res) => {
            // 保存租户id
            tokenStore.tenantId = res.data.tenantId
            //APP(Android和IOS)更新检查 证书更新检查
            checkCertificate(res.data.tenantId)
          })
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      },
    })
  } else {
    // 使用默认租户
    checkCertificate(config.tenantId_global)
  }
}
// 检查证书版本
function checkCertificate(tenantId) {
  //获取缓存和本地版本进行对比
  var certificateVersion, certificateData
  try {
    certificateVersion = kvStore.get('certificateVersion', true)
    certificateData = kvStore.get('certificateData', true)
  } catch (e) {
    console.log(e)
  }
  console.log('certificateVersion', certificateVersion)
  if (certificateVersion && certificateData) {
    // 判断本地缓存的版本号和全局变量哪个高，如果本地的高，存最新的到缓存中，否则直接更新
    let localVersion = getNumVersion(config.certificate_version)
    let catchVersion = getNumVersion(certificateVersion)
    if (localVersion != 0 && catchVersion != 0) {
      if (localVersion > catchVersion) {
        kvStore.set('certificateVersion', config.certificate_version)
        kvStore.set('certificateData', config.certificate_data)
        //使用本地默认版本
        updateCertificate(config.certificate_version, tenantId)
      } else {
        // 使用缓存版本
        updateCertificate(certificateVersion, tenantId)
      }
    } else {
      uni.showToast({
        title: '证书版本号格式有误',
        icon: 'none',
        success() {
          setTimeout(() => {
            plus.runtime.quit()
          }, 1000)
        },
      })
    }
  } else {
    kvStore.set('certificateVersion', config.certificate_version)
    kvStore.set('certificateData', config.certificate_data)
    updateCertificate(config.certificate_version, tenantId)
  }
}
function getNumVersion(version) {
  if (version.indexOf('.') == 1 && version.lastIndexOf('.') == 3) {
    var a = version.charAt(0)
    var b = version.charAt(2)
    var c = version.charAt(4)
    return parseInt(a + b + c)
  } else {
    return 0
  }
}
// 证书更新
var count = 0
function updateCertificate(certificateVersion, tenantId) {
  let deviceNet = ''
  uni.getNetworkType({
    success: function (res) {
      deviceNet = res.networkType
    },
  })
  let deviceType = uni.getSystemInfoSync().platform
  let deviceId = plus.device.uuid
  let deviceFirm = plus.device.vendor
  let deviceModel = plus.device.model
  let deviceIdType = ''
  if (deviceType == 'android') {
    deviceIdType = plus.device.imei
  } else {
    deviceIdType = plus.device.idfa
  }
  systemUpdate({
    softName: config.softName,
    softVersion: certificateVersion,
    softType: 'certificate',
    tenantId: tenantId,
    packName: appPackage,
    deviceType: deviceType,
    deviceId: deviceId,
    deviceIdType: deviceIdType,
    deviceFirm: deviceFirm,
    deviceModel: deviceModel,
    deviceNet: deviceNet,
  })
    .then((res) => {
      console.log('证书更新', res)
      if (res && res.success && res.data.code == 'nothing') {
        // 已是最新
        let cerData
        try {
          cerData = kvStore.get('certificateData', true)
        } catch (e) {
          console.log(e)
        }
        checkIsCer(cerData, tenantId)
      } else {
        //需要更新
        kvStore.set('certificateVersion', res.data.data.softVersion)
        kvStore.set('certificateData', res.data.data.base64str)
        checkIsCer(res.data.data.base64str, tenantId)
      }
    })
    .catch((err) => {
      // 接口出错 调三次确保排除系统故障之外的因素
      console.log('接口出错', err)
      if (count < 3) {
        count++
        updateCertificate(certificateVersion, tenantId)
      } else {
        var cerData
        try {
          cerData = kvStore.get('certificateData', true)
        } catch (e) {
          console.log(e)
        }
        checkIsCer(cerData, tenantId)
      }
    })
}
//证书校验
function checkIsCer(cer, tenantId) {
  uni.configMTLS({
    certificates: [
      {
        host: config.certificateHost,
        server: ['data:cert/pem;base64,' + cer],
      },
    ],
    success({ code }) {
      console.log('证书校验', code)
      if (code == 0) {
        // 验证成功调用app更新接口
        checkAPPVersion(tenantId)
      } else {
        failTip()
      }
    },
    fail(err) {
      failTip()
    },
  })
}
// 验证失败退出
const failTip = () => {
  uni.showToast({
    title: '证书验证失败，请联系管理员',
    icon: 'none',
    success() {
      setTimeout(() => {
        plus.runtime.quit()
      }, 1000)
    },
  })
}
// 检查打包签名证书sha1值
function checkCerSha() {
  // #ifdef APP-PLUS
  // 签名证书检验
  var platform = uni.getSystemInfoSync().platform
  var sign = plus.navigator.getSignature()
  if ('android' == platform) {
    //Android平台
    if (cerSha1 !== sign) {
      //证书不对时退出应用
      uni.showModal({
        title: '错误',
        content: '应用被破坏，无法正常运行！',
        showCancel: false,
        success: () => {
          plus.runtime.quit()
        },
      })
    } else {
      // 权限获取
      // checkPermission()
      // checkMicrophonePermission()
      // APP检查更新
      checkTenant()
    }
  } else {
    //iOS平台
    if (iosMd5 !== sign) {
      //不进入应用或循环弹出提示框
      uni.showModal({
        title: '错误',
        content: '应用被破坏，无法正常运行！',
        showCancel: false,
        success: () => {
          checkCerSha()
        },
      })
    } else {
      // 权限获取
      // checkPermission()
      // checkMicrophonePermission()
      // APP检查更新
      checkTenant()
    }
  }
  // #endif
}
function checkTenant() {
  if (config.isText_tenantId) {
    var tenantName = tokenStore.tenantName
    uni.showModal({
      title: '租户名称',
      editable: true,
      placeholderText: tenantName ? tenantName : '请输入租户名称',
      confirmText: '确认',
      cancelText: '取消', // 取消按钮的文字
      success(res) {
        // 更新store中的tenantName
        tokenStore.tenantName = res.content
        if (res.confirm) {
          // 向登录页传递租户名称，进行表单展示
          uni.$emit('updateTenantName', {
            tenantName: res.content,
          })
          // 获取对应租户id
          getTrarenInfo({
            tenantLoginName: res.content ? res.content : 'system',
          }).then((res) => {
            // 保存租户id
            tokenStore.tenantId = res.data.tenantId
            //APP(Android和IOS)更新检查 证书更新检查
            checkAPPVersion1('startcheck', config.tenantId_global)
          })
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      },
    })
  } else {
    // 使用默认租户
    checkAPPVersion1('startcheck', config.tenantId_global)
    // updateAppDemo(config.tenantId_global,'app')
  }
}
// 极光推送
jpushInit()

// #endif
import { isDev } from '@/common/EnvUtils.js'
onShow(() => {
  console.log('App Show')
  // #ifdef H5 || H5-DINGTALK
  if (isDev()) {
    const vConsole = new VConsole()
  }
  // #endif
})

// ******************** 免密登录****************************//

const hasLogin = ref(mainStore.hasLogin)

let pageName = ''
let visiterObj = {}

// 初始化配置
function initConfig() {
  pageName = getQueryVariable().pageName
  // 其他初始化逻辑
}
function getQueryVariable() {
  localStorage.setItem('urljumpInfo', window.location.href)
  var geturl = window.location.href
  // var geturl = 'http://172.16.11.83:10001/zhxzapp/#/?pageName=carPlace-123123123123123123'
  // var geturl =  'https://sdltzhxz.jsbnb.top/sdlt-zhyq-dd/index.html?pageName=parkList'
  let getqyinfo = geturl.split('?')[1]
  if (!getqyinfo) {
    return false
  }

  let getqys = getqyinfo.split('&')
  let obj = {} //创建空对象，接收截取的参数
  for (let i = 0; i < getqys.length; i++) {
    let item = getqys[i].split('=')
    let key = item[0]
    let value = item[1]
    obj[key] = value
  }

  return obj
}

// 检查用户登录状态
function checkLogin() {
  if (!hasLogin.value) {
    tokenStore.clearAuthInfoToken()
    getCode()
  } else {
    if (tokenStore.value) {
      getUserInfo({
        token: tokenStore.value,
      })
        .then((res1) => {
          handlePageJump()
        })
        .catch((err) => {
          tokenStore.clearAuthInfoToken()
          getCode()
        })
    } else {
      tokenStore.clearAuthInfoToken()
      getCode()
    }
  }
}

// 处理页面跳转
function handlePageJump() {
  if (pageName) {
    // 把url存储在store里

    if (pageName.indexOf('carPlace') != -1) {
      const pageNames = pageName.split('-')
      visiterObj.id = pageNames[1]
      uni.reLaunch({
        url:
          '/pages/vehicle-management/parking-record/detail?id=' + visiterObj.id,
      })
      return
    }

    if (pageName.indexOf('carOrder') != -1) {
      const pageNames = pageName.split('-')
      visiterObj.id = pageNames[1]
      uni.reLaunch({
        url:
          '/pages/vehicle-management/parking-order/detail?id=' + visiterObj.id,
      })
      return
    }
    if (pageName.indexOf('parkList') != -1) {
     
      const pageNames = pageName.split('-')
      visiterObj.id = pageNames[1]
      uni.reLaunch({
        url:
          '/pages/vehicle-management/parking-order/index',
      })
      return
    }
    //  if (pageName.indexOf('carOrder') != -1) {
    //   const pageNames = pageName.split('-')
    //   visiterObj.id = pageNames[1]
    //   uni.reLaunch({
    //     url:
    //       '/pages/paymentCenter/my-account-list/index?accountListId=' + visiterObj.id,
    //   })
    //   return
    // }
  }

  uni.reLaunch({ url: '/pages/home/<USER>' })
}

// 通过钉钉获取code
function getCode() {
  if (dingtalk.env.platform !== 'notInDingTalk') {
    uni.showLoading({
      title: '授权登录中...',
    })
    dingtalk.ready(() => {
      dingtalk.runtime.permission.requestAuthCode({
        corpId: config.corpId,
        onSuccess: (res) => {
          const { code } = res
          handleDingTalkLogin(code)
        },
        onFail: (err) => {
          uni.hideLoading()
          uni.showToast({
            title: '授权失败',
            icon: 'none',
            mask: true,
          })
          uni.reLaunch({ url: '/pages/login/login' })
        },
      })
    })
  } else {
    uni.reLaunch({ url: '/pages/login/login' })
  }
}
// 钉钉登录
async function handleDingTalkLogin(code) {
  try {
    const res = await dingLogin({
      appLabel: config.appLabel,
      corpId: config.corpId,
      code: code,
      type: 'enterprise_internal',
    })
    uni.hideLoading()

    keepUserInfo(res.data)
  } catch (error) {
    uni.showToast({
      title: '授权失败',
      icon: 'none',
      mask: true,
    })
    uni.reLaunch({ url: '/pages/login/login' })
    uni.hideLoading()
  }
}

//保存用户信息
function keepUserInfo(result) {
  // 更新个人信息到vuex
  tokenStore.$patch((state) => {
    state.refreshToken = result.refreshToken
    state.tokenType = result.tokenType
    state.value = result.value
    state.expiration = result.expiration
    // state.clientId = result.clientId
    state.userid = result.additionalInformation.userid
    state.tenantId = result.additionalInformation.customParam.tenantId
  })
  mainStore.login(Platform)
  // 携带token 调取接口获取个人
  getUserInfo({
    token: result.value,
  })
    .then((res1) => {
      // console.log('个人信息',res1)
      // 保存个人信息
      userStore.userInfo = res1
      handleLoginSuccess()
    })
    .catch((err) => {
      //token过期
      jump2Login()
    })
}
function jump2Login() {
  uni.reLaunch({
    url: '/pages/login/login',
  })
}
// 登录成功后，处理函数
function handleLoginSuccess() {
  handlePageJump()
}
</script>

<style lang="scss">
@import '@/uni_modules/uview-plus/index.scss';
</style>
