import{m as e,a as s,o as t,c as a,s as o,n as u,k as n}from"./index-4d380fda.js";import{_ as r}from"./_plugin-vue_export-helper.1b428a4d.js";const i=r({name:"u-safe-bottom",mixins:[e,s,{props:{}}],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return uni.$u.deepMerge({},uni.$u.addStyle(this.customStyle))}},mounted(){}},[["render",function(e,s,r,i,m,l){const d=n;return t(),a(d,{class:o(["u-safe-bottom",[!m.isNvue&&"u-safe-area-inset-bottom"]]),style:u([l.style])},null,8,["style","class"])}],["__scopeId","data-v-3dbbff38"]]);export{i as _};
