<template>
  <view class="page-box">
    <dingtalkNavBar :title="title" :hide-nav-bar="true"></dingtalkNavBar>
    <parkingManagement v-if="activeTab === 'home' "></parkingManagement>
    <parkingRecord v-if="activeTab === 'parkingList' "></parkingRecord>
    <parkingOrder v-if="activeTab === 'parkingOrder' "></parkingOrder>
    <view class="tabbar">
      <view class="tabbar-item" @click="switchTab('home')">
        <text
          class="icon iconfont  icon-shouye"
          :class="{ 'active-icon': activeTab === 'home' }"
        ></text>
        <text :class="{ 'active-icon': activeTab === 'home' }">首页</text>
      </view>
      <view class="tabbar-item" @click="switchTab('parkingList')">
        <text
          class="icon iconfonts icon-tingchejilu"
          :class="{ 'active-icon': activeTab === 'parkingList' }"
        ></text>
        <text :class="{ 'active-icon': activeTab === 'parkingList' }"
          >停车记录</text
        >
      </view>

      <view class="tabbar-item" @click="switchTab('parkingOrder')">
        <text
          class="icon iconfonts icon-tingchedingdan"
          :class="{ 'active-icon': activeTab === 'parkingOrder' }"
        ></text>
        <text :class="{ 'active-icon': activeTab === 'parkingOrder' }"
          >停车订单</text
        >
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import parkingManagement from '/pages/vehicle-management/parking-management/index'
import parkingRecord from '/pages/vehicle-management/parking-record/index'
import parkingOrder from '/pages/vehicle-management/parking-order/index'
const activeTab = ref('home')
const title = ref('停车管理')
const switchTab = (tab) => {
  activeTab.value = tab
  // 这里可以添加切换逻辑，比如显示不同的内容
  switch (tab) {
    case 'home':
        title.value = '停车管理'
          uni.setNavigationBarTitle({
            title: '停车管理'
        })
        // 重新设置标题
    //   uni.navigateTo({
    //     url: '/pages/vehicle-management/parking-management/index',
    //   })
      
      break
    case 'parkingList':
         title.value = '停车记录'
          uni.setNavigationBarTitle({
            title: '停车记录'
        })
    //   uni.navigateTo({
    //     url: '/pages/vehicle-management/parking-record/index',
    //   })
      break
    case 'parkingOrder':
         title.value = '停车订单'
            uni.setNavigationBarTitle({
            title: '停车订单'
        })
    //   uni.navigateTo({
    //     url: '/pages/vehicle-management/parking-order/index',
    //   })
      break
  }
}

</script>

<style lang="scss" scoped>
@import '@/styles/components.scss';



</style>
