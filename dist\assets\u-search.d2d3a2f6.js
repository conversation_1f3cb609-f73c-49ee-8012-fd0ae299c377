import{d as e,m as t,a,aa as o,r as l,b as c,o as s,c as r,w as n,f as i,n as h,e as u,j as d,t as p,g as y,s as m,M as f,l as g,k as _,ab as b}from"./index-0e73d719.js";import{_ as k}from"./u-icon.0412d68c.js";import{_ as C}from"./_plugin-vue_export-helper.1b428a4d.js";const S=C({name:"u-search",mixins:[t,a,{props:{shape:{type:String,default:e.search.shape},bgColor:{type:String,default:e.search.bgColor},placeholder:{type:String,default:e.search.placeholder},clearabled:{type:Boolean,default:e.search.clearabled},focus:{type:Boolean,default:e.search.focus},showAction:{type:Boolean,default:e.search.showAction},actionStyle:{type:Object,default:e.search.actionStyle},actionText:{type:String,default:e.search.actionText},inputAlign:{type:String,default:e.search.inputAlign},inputStyle:{type:Object,default:e.search.inputStyle},disabled:{type:Boolean,default:e.search.disabled},borderColor:{type:String,default:e.search.borderColor},searchIconColor:{type:String,default:e.search.searchIconColor},color:{type:String,default:e.search.color},placeholderColor:{type:String,default:e.search.placeholderColor},searchIcon:{type:String,default:e.search.searchIcon},searchIconSize:{type:[Number,String],default:e.search.searchIconSize},margin:{type:String,default:e.search.margin},animation:{type:Boolean,default:e.search.animation},modelValue:{type:String,default:e.search.value},value:{type:String,default:e.search.value},maxlength:{type:[String,Number],default:e.search.maxlength},height:{type:[String,Number],default:e.search.height},label:{type:[String,Number,null],default:e.search.label}}}],data(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword(e){this.$emit("update:modelValue",e),this.$emit("change",e)},modelValue:{immediate:!0,handler(e){this.keyword=e}}},computed:{showActionBtn(){return!this.animation&&this.showAction}},emits:["clear","search","custom","focus","blur","click","clickIcon","update:modelValue","change"],methods:{inputChange(e){this.keyword=e.detail.value},clear(){this.keyword="",this.$nextTick((()=>{this.$emit("clear")}))},search(e){this.$emit("search",e.detail.value);try{o()}catch(t){}},custom(){this.$emit("custom",this.keyword);try{o()}catch(e){}},getFocus(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur(){setTimeout((()=>{this.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler(){this.disabled&&this.$emit("click")},clickIcon(e){this.$emit("clickIcon",this.keyword);try{o()}catch(t){}}}},[["render",function(e,t,a,o,C,S){const w=g,x=l(c("u-icon"),k),I=_,$=b;return s(),r(I,{class:"u-search",onClick:S.clickHandler,style:h([{margin:e.margin},e.$u.addStyle(e.customStyle)])},{default:n((()=>[i(I,{class:"u-search__content",style:h({backgroundColor:e.bgColor,borderRadius:"round"==e.shape?"100px":"4px",borderColor:e.borderColor})},{default:n((()=>[e.$slots.label||null!==e.label?u(e.$slots,"label",{key:0},(()=>[i(w,{class:"u-search__content__label"},{default:n((()=>[d(p(e.label),1)])),_:1})]),!0):y("",!0),i(I,{class:"u-search__content__icon"},{default:n((()=>[i(x,{onClick:S.clickIcon,size:e.searchIconSize,name:e.searchIcon,color:e.searchIconColor?e.searchIconColor:e.color},null,8,["onClick","size","name","color"])])),_:1}),i($,{"confirm-type":"search",onBlur:S.blur,value:C.keyword,onConfirm:S.search,onInput:S.inputChange,disabled:e.disabled,onFocus:S.getFocus,focus:e.focus,maxlength:e.maxlength,"placeholder-class":"u-search__content__input--placeholder",placeholder:e.placeholder,"placeholder-style":`color: ${e.placeholderColor}`,class:"u-search__content__input",type:"text",style:h([{textAlign:e.inputAlign,color:e.color,backgroundColor:e.bgColor,height:e.$u.addUnit(e.height)},e.inputStyle])},null,8,["onBlur","value","onConfirm","onInput","disabled","onFocus","focus","maxlength","placeholder","placeholder-style","style"]),C.keyword&&e.clearabled&&C.focused?(s(),r(I,{key:1,class:"u-search__content__icon u-search__content__close",onClick:S.clear},{default:n((()=>[i(x,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})])),_:1},8,["onClick"])):y("",!0)])),_:3},8,["style"]),i(w,{style:h([e.actionStyle]),class:m(["u-search__action",[(S.showActionBtn||C.show)&&"u-search__action--active"]]),onClick:f(S.custom,["stop","prevent"])},{default:n((()=>[d(p(e.actionText),1)])),_:1},8,["style","class","onClick"])])),_:3},8,["onClick","style"])}],["__scopeId","data-v-8210c7e3"]]);export{S as _};
