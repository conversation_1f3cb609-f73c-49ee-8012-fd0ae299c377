/* 
 * Iconfont 图标字体样式
 * 统一管理项目中的图标字体
 */

/* 主要图标字体 - iconfont */
@font-face {
  font-family: 'iconfont';
  font-weight: normal;
  font-style: normal;
  src: url('~@/static/iconfont/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  vertical-align: middle;
}

/* 扩展图标字体 - iconfonts */
@font-face {
  font-family: 'iconfonts';
  font-weight: normal;
  font-style: normal;
  src: url('~@/static/iconfonts/iconfont.ttf') format('truetype');
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  vertical-align: middle;
}

/* 通用图标基础样式 */
.icon {
  display: inline-block;
  font-style: normal;
  vertical-align: baseline;
  text-align: center;
  text-transform: none;
  line-height: 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标大小变体 */
.icon-xs { font-size: 12px; }
.icon-sm { font-size: 14px; }
.icon-md { font-size: 16px; }
.icon-lg { font-size: 18px; }
.icon-xl { font-size: 24px; }
.icon-xxl { font-size: 32px; }

/* 图标颜色变体 */
.icon-primary { color: #c20000; }
.icon-success { color: #4cd964; }
.icon-warning { color: #f0ad4e; }
.icon-error { color: #dd524d; }
.icon-info { color: #007aff; }
.icon-muted { color: #999; }
.icon-white { color: #fff; }
.icon-black { color: #000; }

/* iconfont 图标定义 */
.icon-arrow-right:before { content: '\e665'; }
.icon-calendar:before { content: '\e667'; }
.icon-comment:before { content: '\e669'; }
.icon-data-view:before { content: '\e66a'; }
.icon-explain:before { content: '\e66e'; }
.icon-file:before { content: '\e671'; }
.icon-close:before { content: '\e668'; }
.icon-select:before { content: '\e67e'; }
.icon-zhuti:before { content: '\e682'; }
.icon-gangwei:before { content: '\e658'; }
.icon-yinsixieyi:before { content: '\e600'; }
.icon-guanyuwomen:before { content: '\e69a'; }
.icon-xieyi:before { content: '\e66c'; }
.icon-bianjidaimashili:before { content: '\e62d'; }
.icon-shouye:before { content: '\e604'; }

/* iconfonts 图标定义 */
.icon-tingchejilu2:before { content: "\e656"; }
.icon-tingchechang:before { content: "\e6de"; }
.icon-tingchechang1:before { content: "\e619"; }
.icon-tingchejilu1:before { content: "\e60d"; }
.icon-wodecheliang:before { content: "\e615"; }
.icon-tingchechang2:before { content: "\e624"; }
.icon-tingchedingdan:before { content: "\e603"; }
.icon-tingchejilu:before { content: "\e602"; }
.icon-park-record:before { content: "\e600"; }
.icon-jinrujiantou:before { content: "\e88e"; }
.icon-ketuozhuai:before { content: "\e617"; }
.icon-fenge:before { content: "\e62b"; }
.icon-wxbzhanghu:before { content: "\e61e"; }
.icon-qian:before { content: "\e645"; }

/* 响应式图标大小 */
@media screen and (max-width: 768px) {
  .icon-responsive {
    font-size: 14px;
  }
}

@media screen and (min-width: 769px) {
  .icon-responsive {
    font-size: 16px;
  }
}

/* 动画效果 */
.icon-spin {
  animation: icon-spin 1s linear infinite;
}

@keyframes icon-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.icon-pulse {
  animation: icon-pulse 1s ease-in-out infinite alternate;
}

@keyframes icon-pulse {
  0% { opacity: 1; }
  100% { opacity: 0.5; }
}
