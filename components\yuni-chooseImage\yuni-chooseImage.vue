<template>
	<view>
		<u-popup :show="ciShow" mode="bottom" round="4" :closeOnClickOverlay="true" @close="close" @open="open">
			<view class="container">
				<u-button :hairline="false" :customStyle="btnStyle" class="ci-button ci-item" @click="chooseCamera">
					{{ isVideo ? '拍摄' : '拍照' }}
				</u-button>
				<view class="ci-line-little">
					
				</view>
				<u-button :hairline="false" :customStyle="btnStyle" class="ci-button ci-item" @click="chooseAlbum">
					从相册选择
				</u-button>
				<view class="ci-line">
					
				</view>
				<u-button :hairline="false" :customStyle="btnStyle" class="ci-button ci-item" @click="cancel">
					取消
				</u-button>
			</view>
		</u-popup>
	</view>
</template>

<script setup>
	import {ref} from 'vue'
	import permission from "@/common/app-plus/permission.js";
	import kvStore from '@/common/store/uniKVStore.js'
	
	const props =  defineProps({
		ciShow:{
			type: Boolean,
			default: false
		},
		count:{
			type:Number,
			default: 9
		},
		sourceType: {
			default: ['camera','album']
		},
		sizeType: {
			default: ['original', 'compressed'] 
		},
		crop:{
			default:{}
		},
		extension: {
			default:[]
		},
		// 视频or图片
		isVideo:{
			type: Boolean,
			default: false
		},
		// 是否压缩视频。默认true,需要压缩
		compressed:{
			type: Boolean,
			default: true
		},
		// 视频最长拍摄时间，单位秒。最长60
		maxDuration:{
			type:Number,
			default:60
		}
	})
	// ciUpload:上传成功回调
	const emit = defineEmits(['ciClose', 'ciOpen','ciUpload'])
	
	const btnStyle = {
		border:'none'
	}
	const close = () => {
		emit('ciClose')
	}
	const cancel = () => {
		emit('ciClose')
	}
	const open = () => {
		emit('ciOpen')
	}
	const chooseCamera = async () => {
		console.log('拍摄');
		// #ifdef APP-PLUS
		if(uni.getSystemInfoSync().platform == "android"){
		let auCamera =  kvStore.get('auth_camera', true)
		console.log('本地相机权限状态',auCamera);
		if(auCamera){
			let status = permission.isIOS ?
				await permission.requestIOS('camera') :
				await permission.requestAndroid('android.permission.CAMERA')
			if(status == 1){
				if(props.isVideo){
					uni.chooseVideo({
						sourceType: ['camera'],
						compressed: props.compressed,
						maxDuration: props.maxDuration,
						extension: props.extension,
						success: function (res) {
							emit('ciUpload',res)
						}
					});
				}else {
					uni.chooseImage({
						count: props.count,
						sourceType: ['camera'],
						sizeType: props.sizeType,
						crop: props.crop,
						extension: props.extension,
						success: (res) => {
							emit('ciUpload',res)
						},
					})
				}
				
			}else if(status == 0){
				return
			}else {
				permission.judgePermission("camera",(res)=>{
					console.log("judgeResult=====camera"+JSON.stringify(res))
				});
			}
		}else {
			uni.showModal({
			  title: '权限申请',
			  content: '为了正常使用拍照服务，图片上传，图片识别服务，请允许使用摄像头权限。你可以通过系统“设置”进行权限的管理',
			  confirmText: '继续',
			  cancelText: '关闭',
			  success: async (r) => {
			    if (r.confirm) {
					let status = permission.isIOS ?
						await permission.requestIOS('camera') :
						await permission.requestAndroid('android.permission.CAMERA')
					if(status == 1){
						kvStore.set('auth_camera', true)
						if(props.isVideo){
							uni.chooseVideo({
								sourceType: ['camera'],
								compressed: props.compressed,
								maxDuration: props.maxDuration,
								extension: props.extension,
								success: function (res) {
									emit('ciUpload',res)
								}
							});
						}else {
							uni.chooseImage({
								count: props.count,
								sourceType: ['camera'],
								sizeType: props.sizeType,
								crop: props.crop,
								extension: props.extension,
								success: (res) => {
									emit('ciUpload',res)
								},
							})
						}
					}else if(status == 0){
						return
					}else {
						kvStore.set('auth_camera', true)
						permission.judgePermission("camera",(res)=>{
							console.log("judgeResult=====camera"+JSON.stringify(res))
						});
					}
			    } else if (r.cancel) {
					return
				}
			  }
			})
		}
		}else {
			if(props.isVideo){
				uni.chooseVideo({
					sourceType: ['camera'],
					compressed: props.compressed,
					maxDuration: props.maxDuration,
					extension: props.extension,
					success: function (res) {
						emit('ciUpload',res)
					}
				});
			}else {
				uni.chooseImage({
					count: props.count,
					sourceType: ['camera'],
					sizeType: props.sizeType,
					crop: props.crop,
					extension: props.extension,
					success: (res) => {
						emit('ciUpload',res)
					},
				})
			}
		}
		// #endif
		// #ifndef APP-PLUS
			if(props.isVideo){
				uni.chooseVideo({
					sourceType: ['camera'],
					compressed: props.compressed,
					maxDuration: props.maxDuration,
					extension: props.extension,
					success: function (res) {
						emit('ciUpload',res)
					}
				});
			}else {
				uni.chooseImage({
					count: props.count,
					sourceType: ['camera'],
					sizeType: props.sizeType,
					crop: props.crop,
					extension: props.extension,
					success: (res) => {
						emit('ciUpload',res)
					},
				})
			}
		// #endif
	}
	const chooseAlbum = async () => {
		console.log('从相册选择');
		// #ifdef APP-PLUS
		if(uni.getSystemInfoSync().platform == "android"){
		let authImage = kvStore.get('auth_Image', true)
		console.log('本地相册权限状态',authImage);
		if(authImage){
			let status = permission.isIOS ?
				await permission.requestIOS('photoLibrary') :
				await permission.requestAndroid('android.permission.READ_EXTERNAL_STORAGE')
			if(status == 1){
				if(props.isVideo){
					uni.chooseVideo({
						sourceType: ['album'],
						compressed: props.compressed,
						maxDuration: props.maxDuration,
						extension: props.extension,
						success: function (res) {
							emit('ciUpload',res)
						}
					});
				}else {
					uni.chooseImage({
						count: props.count,
						sourceType: ['album'],
						sizeType: props.sizeType,
						crop: props.crop,
						extension: props.extension,
						success: (res) => {
							emit('ciUpload',res)
						},
					})
				}
			}else if(status == 0){
				return
			}else {
				permission.judgePermission("photoLibrary",(res)=>{
					console.log("judgeResult=====photoLibrary"+JSON.stringify(res))
				});
			}
		}else {
			uni.showModal({
			  title: '权限申请',
			  content: '为了正常使用图片上传、图片识别服务，请允许使用存储权限。你可以通过系统“设置”进行权限的管理',
			  confirmText: '继续',
			  cancelText: '关闭',
			  success: async (r) => {
			    if (r.confirm) {
					let status = permission.isIOS ?
						await permission.requestIOS('photoLibrary') :
						await permission.requestAndroid('android.permission.READ_EXTERNAL_STORAGE')
					if(status == 1){
						kvStore.set('auth_Image', true)
						if(props.isVideo){
							uni.chooseVideo({
								sourceType: ['album'],
								compressed: props.compressed,
								maxDuration: props.maxDuration,
								extension: props.extension,
								success: function (res) {
									emit('ciUpload',res)
								}
							});
						}else {
							uni.chooseImage({
								count: props.count,
								sourceType: ['album'],
								sizeType: props.sizeType,
								crop: props.crop,
								extension: props.extension,
								success: (res) => {
									emit('ciUpload',res)
								},
							})
						}
					}else if(status == 0){
						return
					}else {
						kvStore.set('auth_Image', true)
						permission.judgePermission("photoLibrary",(res)=>{
							console.log("judgeResult=====photoLibrary"+JSON.stringify(res))
						});
					}
			    } else if (r.cancel) {
					return
				}
			  }
			})
		}
		}else {
			if(props.isVideo){
				uni.chooseVideo({
					sourceType: ['album'],
					compressed: props.compressed,
					maxDuration: props.maxDuration,
					extension: props.extension,
					success: function (res) {
						emit('ciUpload',res)
					}
				});
			}else {
				uni.chooseImage({
					count: props.count,
					sourceType: ['album'],
					sizeType: props.sizeType,
					crop: props.crop,
					extension: props.extension,
					success: (res) => {
						emit('ciUpload',res)
					},
				})
			}
		}
		// #endif
		// #ifndef APP-PLUS
			if(props.isVideo){
				uni.chooseVideo({
					sourceType: ['album'],
					compressed: props.compressed,
					maxDuration: props.maxDuration,
					extension: props.extension,
					success: function (res) {
						emit('ciUpload',res)
					}
				});
			}else {
				uni.chooseImage({
					count: props.count,
					sourceType: ['album'],
					sizeType: props.sizeType,
					crop: props.crop,
					extension: props.extension,
					success: (res) => {
						emit('ciUpload',res)
					},
				})
			}
		// #endif
	}
</script>

<style lang="scss" scoped>
.container {
	height: 400rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
.ci-line-little {
	height: 1rpx;
	width: 100%;
	background-color: #F5F5F5;
}
.ci-line {
	height: 15rpx;
	width: 100%;
	background-color: #F5F5F5;
}
.ci-item {
	// margin: 20rpx 0;
	flex: 1;
}
</style>