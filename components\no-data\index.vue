<template>
    <view class="no-data">
      <!-- 图片使用静态路径（无需 baseUrlImg 时可删除相关代码） -->
      <image 
        class="no-data-image" 
        src="@/static/images/<EMAIL>" 
        mode="widthFix"
      ></image>
      <text class="no-data-text">暂无数据</text>
    </view>
  </template>
  
  <script setup>
  // 如需使用全局配置中的图片路径可取消注释以下代码
  // import { ref } from 'vue'
  // const baseUrlImg = ref(getApp().globalData.config?.baseUrlImg || '')
  </script>
  
  <style lang="scss" scoped>
  .no-data {
    width: 100%;
    text-align: center;
    
    .no-data-image {
      width: 438upx;
      margin-top: 50upx;
    }
    
    .no-data-text {
      display: inline-block;
      width: 100%;
      text-align: center;
      font-size: 28upx;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #7E848D;
      margin-bottom: 30upx;
    }
  }
  </style>