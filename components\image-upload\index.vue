<template>
  <!-- 上传视频或者图片 -->
  <view class="up-page">
    <!--图片-->
    <view class="show-box" v-for="(item, index) in imageList" :key="index">
      <!-- <image
        class="full"
        :src="item.url"
        :data-src="image"
        @tap="previewImage(item)"
      >
      </image> -->
       <PreviewImage :isShowCMSUrl="true" class="full" :photo-id="item.fileId" custom-class="custom-image" />
      <view class="delect-icon" @tap="delect(index)">
        <image class="full" :src="clearIcon" mode=""></image>
      </view>
    </view>
    <!--视频-->
    <view class="show-box" v-for="(item1, index1) in videoList" :key="index1">
      <video class="full" :src="item1"></video>
      <view class="delect-icon" @tap="delectVideo(index1)">
        <image class="full" :src="clearIcon" mode=""></image>
      </view>
    </view>
    <view v-if="VideoOfImagesShow" @tap="chooseVideoImage" class="box-mode">
      <image class="full" :src="selectfile" mode=""></image>
    </view>
  </view>
</template>
<script setup>
// import { uploadImg, uploadVideo } from '@/api/upload'
import { config } from '@/config/config.js' //appCode,clientId,BASEURL
import { useTokenStore } from '@/store/token.js'
let apiHeader = config.apiHeader
let apiHeader2 = config.apiHeader2
const BASEURL = config.BASEURL
const tokenStore = useTokenStore()
import { ref, reactive, onMounted, watch } from 'vue'
const emit = defineEmits(['getImgUploadIds', 'getVideoUploadIds'])

var sourceType = [['camera'], ['album'], ['camera', 'album']]

const clearIcon = ref(
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDE2YTQgNCAwIDAgMSA0IDR2MTZINGE0IDQgMCAwIDEtNC00VjB6IiBmaWxsPSJ1cmwoI3BhaW50MF9saW5lYXIpIiBmaWxsLW9wYWNpdHk9Ii45OCIgZmlsdGVyPSJ1cmwoI2ZpbHRlcjBfYikiLz48cGF0aCBkPSJNMTAuOTQgOS45OTlsMi44NjMtMi44NTdhLjY2OS42NjkgMCAxIDAtLjk0Ni0uOTQ2TDEwIDkuMDYgNy4xNDMgNi4xOTZhLjY2OS42NjkgMCAwIDAtLjk0Ni45NDZsMi44NjQgMi44NTctMi44NjQgMi44NTdhLjY2Ni42NjYgMCAwIDAgLjIxNyAxLjA5Mi42NjQuNjY0IDAgMCAwIC43MjktLjE0NkwxMCAxMC45MzhsMi44NTcgMi44NjRhLjY2Ny42NjcgMCAwIDAgMS4wOTItLjIxNy42NjYuNjY2IDAgMCAwLS4xNDYtLjcyOUwxMC45MzkgMTB6IiBmaWxsPSIjZmZmIi8+PGRlZnM+PGZpbHRlciBpZD0iZmlsdGVyMF9iIiB4PSItNCIgeT0iLTQiIHdpZHRoPSIyOCIgaGVpZ2h0PSIyOCIgZmlsdGVyVW5pdHM9InVzZXJTcGFjZU9uVXNlIiBjb2xvci1pbnRlcnBvbGF0aW9uLWZpbHRlcnM9InNSR0IiPjxmZUZsb29kIGZsb29kLW9wYWNpdHk9IjAiIHJlc3VsdD0iQmFja2dyb3VuZEltYWdlRml4Ii8+PGZlR2F1c3NpYW5CbHVyIGluPSJCYWNrZ3JvdW5kSW1hZ2UiIHN0ZERldmlhdGlvbj0iMiIvPjxmZUNvbXBvc2l0ZSBpbjI9IlNvdXJjZUFscGhhIiBvcGVyYXRvcj0iaW4iIHJlc3VsdD0iZWZmZWN0MV9iYWNrZ3JvdW5kQmx1ciIvPjxmZUJsZW5kIGluPSJTb3VyY2VHcmFwaGljIiBpbjI9ImVmZmVjdDFfYmFja2dyb3VuZEJsdXIiIHJlc3VsdD0ic2hhcGUiLz48L2ZpbHRlcj48bGluZWFyR3JhZGllbnQgaWQ9InBhaW50MF9saW5lYXIiIHgxPSIyMCIgeDI9IjE1LjU4NiIgeTI9IjIyLjk0IiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHN0b3Agc3RvcC1jb2xvcj0iIzBEMUUyOCIgc3RvcC1vcGFjaXR5PSIuOCIvPjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0iIzA1MEUxMiIgc3RvcC1vcGFjaXR5PSIuNjUiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48L3N2Zz4=',
)
const selectfile = ref(
  'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIuMjUiIHk9Ii4yNSIgd2lkdGg9IjYzLjUiIGhlaWdodD0iNjMuNSIgcng9IjMuNzUiIGZpbGw9IiNGMkYyRjIiIHN0cm9rZT0iI0YyRjJGMiIgc3Ryb2tlLXdpZHRoPSIuNSIvPjxyZWN0IHg9IjE2IiB5PSIzMSIgd2lkdGg9IjMyIiBoZWlnaHQ9IjIiIHJ4PSIxIiBmaWxsPSIjQkZCRkJGIi8+PHJlY3QgeD0iMzMiIHk9IjE2IiB3aWR0aD0iMzIiIGhlaWdodD0iMiIgcng9IjEiIHRyYW5zZm9ybT0icm90YXRlKDkwIDMzIDE2KSIgZmlsbD0iI0JGQkZCRiIvPjwvc3ZnPg==',
)
const VideoOfImagesShow = ref(true) // 页面图片或视频数量超出后，拍照按钮隐藏
const imageList = ref([]) //存放图片的地址
const videoList = ref([]) //视频存放的地址
const videoListIds = ref([]) //存放视频id的地址
// const sourceType = ref(['拍摄', '相册', '拍摄或相册'])
const sourceTypeIndex = ref(2)
const cameraList = ref([
  {
    value: 'back',
    name: '后置摄像头',
    checked: 'true',
  },
  {
    value: 'front',
    name: '前置摄像头',
  },
])
const cameraIndex = ref(0) // 上传视频时的数量
const props = defineProps({
  // 上传配置项
  uploadConfig: {
    type: Object,
    required: true,
    default: () => {
      return {
        //    maxCount: 3,
        //     exParam: 'imgArr'
      }
    },
  },
})
// onUnload((val) => {
//   ;(imageList.value = []),
//     (sourceTypeIndex.value = 2),
//     (sourceType.value = ['拍摄', '相册', '拍摄或相册'])
// }),
// 点击上传图片或视频
function chooseVideoImage() {
  chooseImages()
  //   uni.showActionSheet({
  //     title: '选择上传类型',
  //     // itemList: ['图片', '视频'],
  //      itemList: ['图片'],
  //     success: (res) => {
  //       if (res.tapIndex == 0) {
  //         chooseImages()
  //       } else {
  //         chooseVideo()
  //       }
  //     }
  //   })
}
//上传图片
function chooseImages() {
  uni.chooseImage({
    count: props.uploadConfig.maxCount, // 允许选择的数量
    sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
    sourceType: ['album', 'camera'], // 从相册选择

    success: (e) => {
      uni.showLoading({
        mask: true,
        title: '上传中，请稍侯',
      })

      let tempFilePaths = e.tempFilePaths
      uni.showLoading({
        title: '加载中',
        mask: true,
      })
      uni.uploadFile({
        url:
          BASEURL +
          '/park' +
          apiHeader +
          '/majorParking/userCar/app/uploadFile', //仅为示例，非真实的接口地址

        filePath: tempFilePaths[0],
        name: 'file',
        header: {
          Authorization: `${tokenStore.tokenType}${tokenStore.value}`,
        },
        formData: props.uploadConfig.exParam,
        success: (res) => {
          let data = JSON.parse(res.data)
          // 成功：获取到头像
          if (data.code == '1') {
            imageList.value.push({
              name: data.data.fileName,
              url: data.data.url,
              fileId: data.data.fileId,
            })

   
            emit('getImgUploadIds', imageList.value)
            // 更新当前页面数据
            uni.showToast({
              title: '上传成功',
              icon: 'none',
            })
          } else {
            uni.showToast({
              title: data.message,
              icon: 'none',
            })
          }
          uni.hideLoading()
        },
      })
    
    },
  })
}
//上传视频
function chooseVideo(index) {
  uni.chooseVideo({
    maxDuration: 60, //拍摄视频最长拍摄时间，单位秒。最长支持 60 秒
    count: props.uploadConfig.maxCount,
    camera: cameraList.value[cameraIndex.value].value, //'front'、'back'，默认'back'
    sourceType: sourceType[sourceTypeIndex.value],

    success: (e) => {
      uni.showLoading({
        mask: true,
        title: '上传中，请稍侯',
      })
      uploadVideo(e, props.uploadConfig.exParam).then((res) => {
        const ids = getImgUrl(res)
        videoListIds.value.push(ids)
        emit('getVideoUploadIds', videoListIds.value)
        videoList.value = videoList.value.concat(e.tempFilePath)
        uni.hideLoading()
      })
      if (
        imageList.value.length + videoList.value.length ==
        props.uploadConfig.maxCount
      ) {
        VideoOfImagesShow.value = false
      }
    },
  })
}
//预览图片
function previewImage(e) {
  uni.previewImage({
    current: e,
    urls: imageList.value,
  })
}
// 删除图片
function delect(index) {
  uni.showModal({
    title: '提示',
    content: '是否要删除该图片',
    success: (res) => {
      if (res.confirm) {
        imageList.value.splice(index, 1)
        emit('getImgUploadIds', imageList.value)
      }
      if (
        imageList.value.length + videoList.value.length ==
        props.uploadConfig.maxCount
      ) {
        VideoOfImagesShow.value = false
      } else {
        VideoOfImagesShow.value = true
      }
    },
  })
}
// 删除视频
function delectVideo(index) {
  uni.showModal({
    title: '提示',
    content: '是否要删除此视频',
    success: (res) => {
      if (res.confirm) {
        videoList.value.splice(index, 1)
        videoListIds.value.splice(index, 1)
        emit('getVideoUploadIds', videoListIds.value)
      }
      if (
        imageList.value.length + videoList.value.length ==
        props.uploadConfig.maxCount
      ) {
        VideoOfImagesShow.value = false
      } else {
        VideoOfImagesShow.value = true
      }
    },
  })
}
// 处理图片
function getImgUrl(arr) {
  const idArr = []
  let idStr = ''
  arr.forEach((item) => {
    idArr.push(item.data.id)
  })
  idStr = idArr.join(',')
  return idStr
}

// 新增watch监听
watch(imageList, (newVal) => {
  if (newVal.length >= props.uploadConfig.maxCount) {
    VideoOfImagesShow.value = false
  } else {
    VideoOfImagesShow.value = true
  }
}, { deep: true })
onMounted(() => {
  if (props.uploadConfig.fileList && props.uploadConfig.fileList.length > 0) {
    imageList.value = [...props.uploadConfig.fileList]
    // 如果默认文件数量达到最大值，隐藏上传按钮
    if (imageList.value.length >= props.uploadConfig.maxCount) {
      VideoOfImagesShow.value = false
    }
  }
})
</script>

<style lang="scss">
/* 统一上传后显示的盒子宽高比 */
.box-mode {
  width: 20vw;
  height: 20vw;

  border-radius: 8rpx;
  overflow: hidden;
}

.full {
  width: 100%;
  height: 100%;
  :deep(.custom-image){
    width: 100%!important;
    height: 100%!important;
  }
}

.up-page {
  display: flex;
  flex-wrap: wrap;
  display: flex;
  width: 100%;
  .show-box:nth-child(3n) {
    margin-right: 0;
  }
  .show-box {
    position: relative;
    margin-bottom: 4vw;
    margin-right: 4vw;
    @extend .box-mode;

    .delect-icon {
      height: 40rpx;
      width: 40rpx;
      position: absolute;
      right: 0rpx;
      top: 0rpx;
      z-index: 99;
    }
  }
}
</style>