import{p as o,_ as e}from"./u-input.05fd69cd.js";import{m as l,a as r,ai as i,o as t,c as s,w as a,e as n}from"./index-4d380fda.js";import{_ as d}from"./_plugin-vue_export-helper.1b428a4d.js";const c=d({name:"u--input",mixins:[l,o,r],components:{uvInput:e}},[["render",function(o,e,l,r,d,c){const u=i("uvInput");return t(),s(u,{modelValue:o.modelValue,"onUpdate:modelValue":e[0]||(e[0]=e=>o.$emit("update:modelValue",e)),type:o.type,fixed:o.fixed,disabled:o.disabled,disabledColor:o.disabledColor,clearable:o.clearable,password:o.password,maxlength:o.maxlength,placeholder:o.placeholder,placeholderClass:o.placeholderClass,placeholderStyle:o.placeholderStyle,showWordLimit:o.showWordLimit,confirmType:o.confirmType,confirmHold:o.confirmHold,holdKeyboard:o.holdKeyboard,focus:o.focus,autoBlur:o.autoBlur,disableDefaultPadding:o.disableDefaultPadding,cursor:o.cursor,cursorSpacing:o.cursorSpacing,selectionStart:o.selectionStart,selectionEnd:o.selectionEnd,adjustPosition:o.adjustPosition,inputAlign:o.inputAlign,fontSize:o.fontSize,color:o.color,prefixIcon:o.prefixIcon,suffixIcon:o.suffixIcon,suffixIconStyle:o.suffixIconStyle,prefixIconStyle:o.prefixIconStyle,border:o.border,readonly:o.readonly,shape:o.shape,customStyle:o.customStyle,formatter:o.formatter,ignoreCompositionEvent:o.ignoreCompositionEvent},{default:a((()=>[n(o.$slots,"prefix",{slot:"prefix"}),n(o.$slots,"suffix",{slot:"suffix"})])),_:3},8,["modelValue","type","fixed","disabled","disabledColor","clearable","password","maxlength","placeholder","placeholderClass","placeholderStyle","showWordLimit","confirmType","confirmHold","holdKeyboard","focus","autoBlur","disableDefaultPadding","cursor","cursorSpacing","selectionStart","selectionEnd","adjustPosition","inputAlign","fontSize","color","prefixIcon","suffixIcon","suffixIconStyle","prefixIconStyle","border","readonly","shape","customStyle","formatter","ignoreCompositionEvent"])}]]);export{c as _};
