// #ifdef H5||APP-PLUS
import { sm2, sm3 } from 'sm-crypto'
// #endif

// #ifdef MP-WEIXIN||MP-ALIPAY
import { sm2, sm3 } from 'miniprogram-sm-crypto'
// #endif

/**
 * 生成密钥对
 */
export function doGenerate() {
  let keypair = sm2.generateKeyPairHex()
  return {
    publicKey: keypair.publicKey,
    privateKey: keypair.privateKey,
  }
}

/**
 * sm2加密方法
 * @param publickey
 * @param msg
 * @returns {*}
 */
export function doCrypt(msg) {
  let encryptData =
    '04' +
    sm2.doEncrypt(
      msg,
      '0428D625CEEB71CE823BD7D78DFEE7B122F2DA5C4D21E32253AD684' +
        'D0FE21810394A799639C0CDFBFEB535A1DFD6A366A637E582CE0B1466' +
        'A5FE7858841135DE6B',
      1
    )
  return encryptData
}

/**
 * sm2解密方法
 * @param publickey
 * @param msg
 * @returns {*}
 */
export function doDecrypt(prvkey, encryptData, cipherMode) {
  let decryptData = sm2.doDecrypt(encryptData, prvkey, cipherMode) // 解密结果
  return decryptData
}

/**
 * sm3加密
 */
export function doSM3(msg) {
  return sm3(msg)
}
