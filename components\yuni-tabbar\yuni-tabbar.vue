<template>
  <view>
    <u-tabbar
      :value="current ? current : 0"
      @change="changeTab"
      :fixed="true"
      :placeholder="true"
      :safeAreaInsetBottom="true"
    >
      <u-tabbar-item
        v-for="(item, index) in tabBerStore.tabBarList"
        :key="index"
        :text="item.text"
        :name="item.name"
      >
        <template #active-icon>
          <image class="icon-style" :src="item.selectedIconPath"> </image>
        </template>
        <template #inactive-icon>
          <image class="icon-style" :src="item.iconPath"></image>
        </template>
      </u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script setup>
import { useTabBerStore } from '../../store/tabBer'
const props = defineProps(['current'])
const tabBerStore = useTabBerStore()
function changeTab(e) {
  console.log( tabBerStore.tabBarList)
  uni.switchTab({
    url: tabBerStore.tabBarList[e].pagePath,
  })
}
</script>
<style lang="scss" scoped>
.icon-style {
  width: 34rpx;
  height: 34rpx;
}
</style>
