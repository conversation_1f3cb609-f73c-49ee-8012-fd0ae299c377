	// #ifdef APP-PLUS
		import permission from '@/common/app-plus/permission'
		import kvStore from '@/common/store/uniKVStore.js'
	// #endif
	export default async function soundRecord() {
		// #ifdef APP-PLUS
		if(uni.getSystemInfoSync().platform == "android"){
			let authRecord =  kvStore.get('auth_record', true)
			console.log('录音权限状态',authRecord);
			if(authRecord){
				let status = permission.isIOS ?
					await permission.requestIOS('record') :
					await permission.requestAndroid('android.permission.RECORD_AUDIO')
				if(status == 1){
					return uni.getRecorderManager()
				}else if(status == 0){
					uni.hideLoading()
					return
				}else {
					uni.hideLoading()
					permission.judgePermission("record",(res)=>{
						console.log("judgeResult=====record"+JSON.stringify(res))
					});
				}
			}else {
				uni.showModal({
				  title: '权限申请',
				  content: '为了正常录制音频、语音识别等功能，请允许使用麦克风权限。你可以通过系统“设置”进行权限的管理',
				  confirmText: '继续',
				  cancelText: '关闭',
				  success: async (r) => {
					if (r.confirm) {
						let status = permission.isIOS ?
							await permission.requestIOS('record') :
							await permission.requestAndroid('android.permission.RECORD_AUDIO')
						if(status == 1){
							kvStore.set('auth_record', true)
							return uni.getRecorderManager()
						}else if(status == 0){
							uni.hideLoading()
							return
						}else {
							kvStore.set('auth_record', true)
							uni.hideLoading()
							permission.judgePermission("record",(res)=>{
								console.log("judgeResult=====record"+JSON.stringify(res))
							});
						}
					} else if (r.cancel) {
						return
					}
				  }
				})
			}
			}else {
				return uni.getRecorderManager()
			}
		// #endif
		// #ifndef APP-PLUS
			return uni.getRecorderManager()
		// #endif
	}