// common/dict.js

import { ref, toRefs } from 'vue'
import { useDictStore } from '@/common/store/dict'
import { getDictInfo } from '@/common/api'

const successValue = ["valid", "show","ok"];
const dangerValue = ["invalid", "hide","reject"];

export function useDict(...args) {
  const res = ref({});
  const dictStore = useDictStore()
  
  args.forEach((dictType) => {
    res.value[dictType] = []
    const cached = dictStore.getDict(dictType)
    
    if (cached?.length > 0) {
      res.value[dictType] = cached
    } else {
      // uni-app 请求适配
      getDictInfo(dictType).then(resp => {
        const dictData = resp.data.map(p => ({
          label: p.dictLabel,
          value: p.dictValue,
          tagType: getTagType(p) // 修改属性名适配uni-app组件
        }))
        res.value[dictType] = dictData
        dictStore.setDict(dictType, dictData)
      })
    }
  })

  return toRefs(res.value)
}

function getTagType(p) {
  if (['success', 'warning', 'danger', 'info'].includes(p.dictRemark)) {
    return p.dictRemark
  }
  if (successValue.includes(p.dictValue)) return "success"
  if (dangerValue.includes(p.dictValue)) return "error" // 适配uni-app常用type值
  return "primary"
}