import { request } from '../request.js'
import {
  ACCREDIT_ORGANIZATION_URL,
  ACCREDIT_CONTACTS_URL,
} from '@/common/net/netUrl.js'
// 查询跟组织接口(个人)
export function organization(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: ACCREDIT_ORGANIZATION_URL,
    method: 'POST',
    params,
  })
}
// 查询个人通讯录接口
export function contacts(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: ACCREDIT_CONTACTS_URL,
    method: 'GET',
    params,
  })
}
