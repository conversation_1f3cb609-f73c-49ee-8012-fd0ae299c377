<template>
  <view class="yt-txl-container">
    <view class="top_search">
      <u-search
        :clearabled="true"
        :showAction="false"
        bgColor="#F5FCFF"
        borderColor="#EAF7FE"
        placeholderColor="#C4C4C4"
        height="72rpx"
        v-model="searchVal"
        @search="searchStaff"
      ></u-search>
    </view>
    <view :class="scrollObj" :style="index ? '' : 'right:25upx'">
      <scroll-view
		class="main-scroll"
        :scroll-into-view="toIndex"
        scroll-y
		:show-scrollbar="false"
        @scrolltolower="toBottom"
      >
        <!-- <view :id="o.key" v-for="(o, i) in resource" :key="i"> -->
        <!-- <view class="p">{{ o.key }}</view> -->
        <view
          @click="clickFunc(item)"
          v-for="(item, index) in resource"
          class="info"
          :key="index"
        >
          <view class="info_content">
            <view class="icon">
              {{ item.staffName.slice(0, 2) }}
            </view>
            <view class="item">
              <text class="name">{{ item.staffName }}</text>
              <text class="post">{{ item.orgName }} </text>
            </view>
          </view>
          <u-line
            v-show="index != resource.length - 1"
            length="100%"
            color="#E6E6E6"
            margin="20rpx 0rpx 0rpx 0rpx"
          ></u-line>
        </view>
        <!-- </view> -->
      </scroll-view>
    </view>
    <view v-if="noData" class="noData">暂无数据~</view>
  </view>
</template>

<script>
'use strict'
// import { Pinyin } from './pinyin'
// const pinyin = new Pinyin({ charCase: 0 })
import { SEARCH_URL } from '@/common/net/staticUrl.js'
import { contacts } from '@/common/net/address/address.js'
import { config } from '@/config/config.js'
export default {
  props: {
    name: {
      type: String,
      default() {
        return 'staffName'
      },
    },
    index: {
      type: Boolean,
      default: true,
    },
    color: {
      type: String,
      default: '#f44336',
    },
    orgId: {
      type: String,
      default: '',
    },
    tenantId: {
      type: String,
      default: 'system',
    },
  },
  data() {
    return {
      serch_url: SEARCH_URL,
      resource: [],
      chars: [],
      toIndex: '',
      searchVal: '',
      pageNum: 1,
      pageSize: 10,
      total: 0,
      staffDatas: [],
      noData: false,
	  isRefresh: false,
	  isNavBar:true,
	  // #ifdef H5-DINGTALK
	  isNavBar:config.isHideNavBar
	  // #endif
    }
  },
  onPullDownRefresh(){
	  console.log('下拉刷新')
  },
  methods: {
    clickFunc(item) {
      this.$emit('ev', item)
    },
    async searchStaff() {
      this.pageNum = 1
	  this.staffDatas = []
	  this.getInstitutionDetail()
    },
    async getInstitutionDetail() {
      let query = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        orgId: this.orgId,
        tenantId: this.tenantId,
        staffName: this.searchVal,
      }
      const res = await contacts(query) 
      if (res && res.success) {
        this.total = res.data.total
        this.staffDatas = [...this.staffDatas, ...res.data.records]
        if (this.staffDatas.length <= 0) {
          this.noData = true
        }
        this.resource = this.staffDatas
      }
	  console.log("请求完成")
    },
    // 列表触底事件
    toBottom() {
      if (this.total > this.pageNum * this.pageSize) {
        this.pageNum += 1
        this.getInstitutionDetail()
      }
    },
	async refresherrefresh(){
		this.pageNum = 1
		this.staffDatas = []
		await this.getInstitutionDetail()
		setTimeout(() => {
			console.log("setTimeout");
			uni.stopPullDownRefresh()
		}, 2000);
	}
  },
  mounted() {
    this.getInstitutionDetail()
  },
  computed:{
    scrollObj:function(){
      return {
        scroll: this.isNavBar,
        'ding-scorll': !this.isNavBar
      }
    }
  }
}
</script>

<style lang="scss" scoped>
page {
  background: #f4f4f4;
}
.yt-txl-container {
  font-family: $uni-font-family;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  min-height:100vh;
}
.top_search {
	line-height: 80rpx;
	/* 不放大不缩小固定100rpx */
	flex: 0 0 80rpx;
	padding: 24rpx;
	background-color: #fff;
	box-shadow: 0rpx 2rpx 14rpx 0rpx rgba(0, 0, 0, 0.09);
	border-radius: 10rpx;
}
.scroll {
	flex: 1;
	position: relative;
}
.main-scroll {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
}
.noData {
	flex: 1;
	width: 100%;
	display: flex;
	justify-content: center;
}
.organInput {
  height: 74rpx;
  line-height: 74rpx;
}



.ding-scorll {
  position: absolute;
  left: 25rpx;
  top: 150rpx;
  right: 46rpx;
  bottom: 25rpx;
  /* #ifdef H5-DINGTALK */
  bottom: 0rpx;
  top: 130rpx;
  /* #endif */
}

.info {
  margin-bottom: 10rpx;
  padding: 15rpx 25rpx;
  background: #fff;
}
.info_content {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.icon {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 30upx;
  color: #7f7f7f;
  background: linear-gradient(to bottom, #f5f9ff, #e7f0fd);
  border-radius: 50%;
}

.item {
  height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  font-size: 28rpx;
}

.flag {
  position: absolute;
  top: 150rpx;
  right: 20rpx;
  bottom: 25rpx;
}

.flag-scroll {
  padding-top: 10rpx;
}

.flag-key {
  // padding: 0;
  margin: 0 0 8rpx auto;
  color: #aeb7c4;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
}
</style>
