<template>
  <view class="container-table-box">

    <!-- #ifdef H5-DINGTALK -->
    <uni-nav-bar title="停车订单" left-icon="left" v-if="isNavBar" fixed @clickLeft="clickLeft"></uni-nav-bar>
    <!-- #endif -->
    <myTabs :modelData="modelData" :initIndex="initIndex" @change="tabChange"></myTabs>

    <view class="card-container" v-if="dataList.length > 0">
      <scroll-view scroll-y class="scrolls-view" refresher-enabled :refresher-triggered="refresherTriggered"
        style="height: calc(100vh - 80rpx);" lower-threshold="150" @refresherrefresh="handleRefresh"
        @scrolltolower="handleScrollLower">
        <!-- 停车订单列表 -->
        <view class="order-list">
          <view class="order-card" v-for="(item, index) of dataList" :key="index" @click="handleView(item)">
            <!-- 卡片头部 -->
            <view class="order-header">
              <view class="header-left">
                <view class="parking-name">
                  <text class="iconfont icon-tingchechang">🏢</text>
                  <text class="parking-text">{{ item.parkingName || '未知停车场' }}</text>
                </view>
                <view class="order-status" :class="getStatusClass(item.orderStatus)">
                  {{ $formatDictLabel(item.orderStatus, ORDER_STATUS) }}
                </view>
              </view>
              <view class="header-right" @click.stop="handleView(item)">
                <text class="iconfont icon-arrow-right view-icon"></text>
              </view>
            </view>

            <!-- 车牌和人员信息 -->
            <view class="order-main">
              <view class="plate-section">
                <view class="plate-container">
                  <view class="plate-number" :class="getPlateClass(item.plateNo)">
                    {{ formatPlateNo(item.plateNo) }}
                  </view>
                </view>
                <view class="user-info">
                  <view class="user-name" v-if="item.staffName">
                    <text class="iconfont icon-user">👤</text>
                    <text>{{ item.staffName }}</text>
                  </view>
                  <view class="user-phone" v-if="item.cellphone">
                    <text class="iconfont icon-phone">📱</text>
                    <text>{{ item.cellphone }}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 时间信息 -->
            <view class="time-section">
              <view class="time-item">
                <view class="time-dot enter-dot"></view>
                <view class="time-content">
                  <text class="time-label">入场时间</text>
                  <text class="time-value">{{ item.enterTime || '未知' }}</text>
                </view>
              </view>
              <view class="time-line"></view>
              <view class="time-item">
                <view class="time-dot exit-dot"></view>
                <view class="time-content">
                  <text class="time-label">出场时间</text>
                  <text class="time-value">{{ item.outTime || '未出场' }}</text>
                </view>
              </view>
            </view>

            <!-- 停车天数 -->
            <view class="duration-section" v-if="item.parkingDays">
              <view class="duration-info">
                <text class="duration-label">停车天数</text>
                <text class="duration-value">{{ item.parkingDays }} 天</text>
              </view>
            </view>
          </view>
        </view>

        <u-loadmore v-if="total > 0" :status="loadStatus" :load-text="{
          loadmore: '点击加载更多',
          loading: '正在加载...',
          nomore: '没有更多了',
        }" />
      </scroll-view>
    </view>
    <noData v-else></noData>

  
  </view>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance,watchEffect } from 'vue'
import { useUserStore } from '@/store/user.js'
import {
  onShow,
} from '@dcloudio/uni-app'
// 接口
import {
  findParkOrdersPage,
} from '@/common/api/park/record/index'

import myTabs from '@/components/myTabs/myTabs'

// 字典数据
const { proxy } = getCurrentInstance()
const { park_data_status, park_has_permissions, ORDER_STATUS } = proxy.useDict(
  'park_data_status',
  'park_has_permissions',
  'ORDER_STATUS'
)

const initIndex = ref(0)
const modelData = ref([])
const userStore = useUserStore()
import * as myDIng from 'dingtalk-jsapi'
import {
  config
} from '@/config/config.js'
const isNavBar = ref(config.isHideNavBar)
onShow(() => {
  if (isNavBar.value) {
    myDIng.biz.navigation.hideBar({
      hidden: true,
    })
  } else {
    // 钉钉H5设置顶部导航栏标题
    myDIng.biz.navigation.setTitle({
      title: '停车记录', //控制标题文本，空字符串表示显示默认文本
    })
  }
})
// 响应式数据
const dataList = ref([])
const total = ref(0)
const loadStatus = ref('loadmore')
const formData = reactive({
  pageNum: 1,
  pageSize: 10,
  orderStatus: null,
  staffId: userStore.userInfo.staffId
})

// 监听字典加载
watchEffect(() => {
  if (ORDER_STATUS.value.length > 0) {
    // 在字典数据前面添加"全部"选项
    modelData.value = [
      { label: '全部', value: '' },
      ...ORDER_STATUS.value
    ]
    formData.orderStatus = modelData.value[0]?.value ?? ''
  }
})
// 在 script setup 中添加
const refresherTriggered = ref(false) // 下拉刷新状态

// 下拉刷新处理
const handleRefresh = async () => {
  if (refresherTriggered.value) return;
  const startTime = Date.now();
  refresherTriggered.value = true;

  try {
    formData.pageNum = 1;
    await Promise.all([
      getList(),
      // 强制动画至少展示 500ms
      new Promise(resolve => setTimeout(resolve, 500)),
    ]);
  } finally {
    const elapsed = Date.now() - startTime;
    const delay = elapsed < 500 ? 500 - elapsed : 0;
    setTimeout(() => {
      refresherTriggered.value = false;
    }, delay);
  }
};
// 车牌格式化
const formatPlateNo = (plateNo) => {
  return plateNo.replace(/\s/g, '').toUpperCase()
}

const getPlateClass = (plateNo) => {
  const formatted = formatPlateNo(plateNo)
  return {
    'green-plate': formatted.length === 8,
    'blue-plate': formatted.length === 7,
  }
}

// 获取订单状态样式类
const getStatusClass = (status) => {
  const statusMap = {
    '1': 'status-active',    // 进行中
    '2': 'status-completed', // 已完成
    '3': 'status-cancelled'  // 已取消
  }
  return statusMap[status] || 'status-default'
}

// 加载更多
const handleScrollLower = () => {
  if (dataList.value.length >= total.value) return
  formData.pageNum++
  getList()
}

// 获取数据
const getList = async () => {
  try {

    setTimeout(function () {
      uni.hideLoading()
    }, 10000)

    const res = await findParkOrdersPage({
      ...formData,
    },)

    if (res.success) {
      if (formData.pageNum === 1) {
        dataList.value = res.data.records
      } else {
        dataList.value = [...dataList.value, ...res.data.records]
      }
      total.value = res.data.total
      loadStatus.value =
        res.data.records.length < formData.pageSize ? 'nomore' : 'loadmore'
    }
  } finally {
    uni.hideLoading()
  }
}


// 导航返回键
function clickLeft() {
  uni.navigateBack({
    delta: 1,
  })
}
// 弹窗控制
const handleView = (item) => {
  uni.navigateTo({
    url: '/pages/vehicle-management/parking-order/detail?id=' + item.id
  })
}


const tabChange = (index) => {
  formData.orderStatus =  modelData.value[index].value
  formData.pageNum = 1
  initIndex.value = index
  getList()
}
const handleEdit = (item) => {
  diaWindow.open1 = true
  diaWindow.popupType = 'edit'
  diaWindow.dialogTitle = '修改信息'
  diaWindow.dialogFooterBtn = true
  diaWindow.rowData = item
}


// const daySelects = ref([]);
// const daySelects2 = ref([]);

// const handleDateChange = (e) => {
//   // uni 返回的是时间戳数组，需要转换成字符串
//   if(e.detail.value) {
//     daySelects.value = e.detail.value.map(timestamp => {
//       return new Date(timestamp).toISOString().replace('T', ' ').substring(0, 19);
//     });
//   }
// };

// const handleDateChange2 = (e) => {
//   if(e.detail.value) {
//     daySelects2.value = e.detail.value.map(timestamp => {
//       return new Date(timestamp).toISOString().replace('T', ' ').substring(0, 19);
//     });
//   }
// };
// 初始化
onMounted(() => {
  uni.showLoading({
    title: '加载中',
  })
  getList()
})
</script>

<style lang="scss" scoped>
.container-table-box {
  background: #f5f5f5;
  height: calc(100vh - 90rpx);
}

// 确保滚动区域高度计算正确
.card-container {
  height: 100%;

  /* 根据搜索栏高度调整 */
  .scroll-view {
    height: 100%;
  }
}

/* 停车订单列表样式 */
.order-list {
  padding: 20rpx;
  background: #f5f5f5;

  .order-card {
    background: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    border: 2rpx solid #f0f0f0;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    &:active {
      transform: translateY(-2rpx);
      box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
      border-color: #c20000;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 6rpx;
      height: 100%;
      background: linear-gradient(to bottom, #c20000, #e74c3c);
    }

    .order-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;
      padding-bottom: 16rpx;
      border-bottom: 2rpx solid #f5f5f5;

      .header-left {
        display: flex;
        align-items: center;
        flex: 1;

        .parking-name {
          display: flex;
          align-items: center;
          margin-right: 16rpx;

          .iconfont {
            font-size: 24rpx;
            color: #c20000;
            margin-right: 8rpx;
          }

          .parking-text {
            font-size: 28rpx;
            font-weight: 600;
            color: #333;
          }
        }

        .order-status {
          padding: 6rpx 12rpx;
          border-radius: 16rpx;
          font-size: 22rpx;
          font-weight: 500;
          color: #fff;

          &.status-active {
            background: linear-gradient(135deg, #4cd964, #5ac777);
          }

          &.status-completed {
            background: linear-gradient(135deg, #007aff, #5ac8fa);
          }

          &.status-cancelled {
            background: linear-gradient(135deg, #ff3b30, #e74c3c);
          }

          &.status-default {
            background: linear-gradient(135deg, #8e8e93, #aeaeb2);
          }
        }
      }

      .header-right {
        .view-icon {
          font-size: 24rpx;
          color: #999;
          padding: 8rpx;
        }
      }
    }

    .order-main {
      margin-bottom: 20rpx;

      .plate-section {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .plate-container {
          .plate-number {
            font-size: 36rpx;
            font-weight: 600;
            font-family: 'Courier New', monospace;
            letter-spacing: 2rpx;
            padding: 8rpx 16rpx;
            border-radius: 8rpx;
            border: 2rpx solid;

            &.blue-plate {
              color: #007aff;
              background: #e3f2fd;
              border-color: #bbdefb;
            }

            &.green-plate {
              color: #4cd964;
              background: #e8f5e8;
              border-color: #c8e6c9;
            }
          }
        }

        .user-info {
          display: flex;
          flex-direction: column;
          gap: 8rpx;

          .user-name, .user-phone {
            display: flex;
            align-items: center;
            font-size: 26rpx;
            color: #666;

            .iconfont {
              font-size: 20rpx;
              margin-right: 8rpx;
              color: #c20000;
            }
          }
        }
      }
    }

    .time-section {
      position: relative;
      margin-bottom: 20rpx;

      .time-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .time-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          margin-right: 16rpx;
          position: relative;
          z-index: 2;

          &.enter-dot {
            background: #4cd964;
          }

          &.exit-dot {
            background: #ff9500;
          }
        }

        .time-content {
          flex: 1;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .time-label {
            font-size: 26rpx;
            color: #666;
          }

          .time-value {
            font-size: 26rpx;
            color: #333;
            font-weight: 500;
          }
        }
      }

      .time-line {
        position: absolute;
        left: 8rpx;
        top: 16rpx;
        bottom: 16rpx;
        width: 2rpx;
        background: #e0e0e0;
        z-index: 1;
      }
    }

    .duration-section {
      background: #f8f9fa;
      border-radius: 12rpx;
      padding: 16rpx;

      .duration-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .duration-label {
          font-size: 26rpx;
          color: #666;
        }

        .duration-value {
          font-size: 28rpx;
          font-weight: 600;
          color: #c20000;
        }
      }
    }
  }
}

  .table-card-one {
    width: 100%;
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    margin-bottom: 15rpx;
    padding: 20rpx;
    padding-top: 0px;
    box-sizing: border-box;
    position: relative;
    background: #fff;

    .fixed-btn {
      margin-left: 8rpx;
    }

    &:hover {
      box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2); // 悬停时加深阴影
    }

    &:nth-child(4n) {
      margin-right: 0rpx;
    }

    .cars-place {
      padding: 12rpx 0rpx 12rpx 0rpx;
      border-bottom: 1rpx solid #eee;
      margin-bottom: 8rpx;
      display: flex;
      align-items: center;

      .cars-place-box {
        display: inline-block;
        background: #eee;
        color: #303133;
        padding: 8rpx 20rpx;
        border-radius: 6rpx;
        font-size: 28rpx;
      }

      button {
        padding: 0px 20rpx;
      }

      img {
        width: 18rpx;
        margin-right: 5rpx;
        vertical-align: middle;
        margin-top: -3rpx;
      }
    }

    .cars-plateNo {
      display: flex;
      justify-content: center;
      margin-right: 20rpx;
      font-size: 28rpx;
      color: #c20000;
      border-bottom: 1rpx solid #eee;
      cursor: pointer;

      .blue-plate {
        background: blue;
        color: #fff;
        border-radius: 8rpx;
        padding: 8rpx 12rpx;
      }

      .green-plate {
        background: green;
        color: #fff;
        border-radius: 8rpx;
        padding: 8rpx 12rpx;
      }
    }

    .card-top-row {
      display: flex;
      align-items: center;
      border-bottom: 1rpx solid #eee;
      padding-bottom: 10rpx;

      .el-button {
        font-size: 13rpx;
        height: 30rpx;
      }

      .left-dollor {
        .changft-2 {
          display: inline-block;
          background-color: #eee;
          border-radius: 8rpx;
          color: #303133;
          font-size: 28rpx;
          padding: 8rpx 12rpx;
        }
      }

      .car-card {
        margin-right: 20rpx;
        background: #c20000;
        color: #ffffff;
        border-radius: 8rpx;
        font-size: 28rpx;
        padding: 8rpx 12rpx;
        display: inline-block;

        &.lanColor {
          background: #eee;
          color: #303133;
        }
      }

      .openDoorBtn {
        background: #c20000;
      }

      .view-icon {
        padding: 10rpx;
        border-radius: 8rpx;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.3s;
        &:hover {
          background: #e0e0e0;
        }
      }
    }

    .card-content {
      margin-top: 10rpx;

      .image-row {
        display: flex;
        align-items: center;

        .image-col {
          width: calc((100% - 20rpx) / 2);
          margin-right: 20rpx;

          &:last-child {
            margin-right: 0px;
          }

          .top-image {
            margin-bottom: 20rpx;
            border-radius: 8rpx;
            background: #eee;
            height: 150rpx;

            :deep(.custom-image) {
              width: 100%;
              height: 100% !important;
              border-radius: 10rpx;
            }
          }
        }
      }

      .mid-content {
        font-size: 28rpx;
        border: 1rpx solid #eee;
        border-bottom: 0px;
        padding: 0px 10rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        white-space: nowrap;
        /* 防止文本换行 */
        overflow: hidden;
        /* 隐藏溢出的内容 */
        text-overflow: ellipsis;
        /* 在文本溢出时显示省略号 */
      }

      .bottom-info {
        border: 1rpx solid #eee;
        padding: 0rpx 10rpx;
        height: 60rpx;
        box-sizing: border-box;

        .info-row {
          display: flex;
          align-items: center;
          height: 100%;

          .info-point {
            width: 14rpx;
            height: 14rpx;
            border-radius: 50%;
            background: #c20000;
            flex-shrink: 0;
            margin-right: 12rpx;
            flex-shrink: 0;
          }

          .info-text {
            flex: 1;
            font-size: 26rpx;
            white-space: nowrap;
            /* 防止文本换行 */
            overflow: hidden;
            /* 隐藏溢出的内容 */
            text-overflow: ellipsis;
            /* 在文本溢出时显示省略号 */
          }
        }
      }
    }
  }


.flex-1 {
  flex: 1;
}

/* 容器样式 */
.search-container {
  padding: 20rpx;
  padding-bottom: 0px;
  background-color: #f5f5f5;
  position: fixed;
  /* 关键属性 */
  top: 90rpx;
  /* 贴顶 */
  left: 0;
  /* 贴左 */
  right: 0;
  /* 贴右 */
  z-index: 999;
  /* 确保悬浮层级 */
}

/* 输入框外层布局 */
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 40rpx;
  padding: 10rpx 20rpx;
}

/* 输入框样式 */
.search-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  padding-right: 80rpx;
  /* 留出图标位置 */
}

/* 搜索图标 */
.search-icon {
  position: absolute;
  right: 30rpx;
  width: 40rpx;
  height: 40rpx;
}

/* 占位符颜色 */
.placeholder-style {
  color: #999;
}
</style>