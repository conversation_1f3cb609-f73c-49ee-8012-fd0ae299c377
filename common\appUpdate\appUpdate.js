import {
	config
} from '@/config/config.js'
import {
	appPackage
} from '@/package.json'
import {
	systemUpdate
} from '@/common/api'
import kvStore from '@/common/store/uniKVStore.js'
// 获取设备信息
const deviceType = uni.getSystemInfoSync().osName
const deviceId = plus.device.uuid
const deviceFirm = plus.device.vendor
const deviceModel = plus.device.model
// 获取缓存信息，用来判断启动时是否提示更新
let notice_appVersion = ''
// inlet:(启动时检查更新:startcheck,手动检查更新:handcheck) 
let inlet = ''
export default function checkAPPVersion1(inletInfo, tenantId) {
	console.log('更新###',inletInfo,tenantId);
	notice_appVersion = kvStore.get("notice_appVersion", true)
	inlet = inletInfo
	let appVersion = plus.runtime.version
	appVersion = appVersion.slice(0)
	let deviceNet = ''
	uni.getNetworkType({
		success: function(res) {
			deviceNet = res.networkType
		},
		complete: function() {
			let params = {
				softName: config.softName,
				softVersion: appVersion,
				softType: 'app',
				tenantId: tenantId,
				packName: appPackage,
				deviceType: deviceType,
				deviceId: deviceId,
				deviceIdType: '',
				deviceFirm: deviceFirm,
				deviceModel: deviceModel,
				deviceNet: deviceNet,
			}
			console.log('整包更新~~~~', params)
			systemUpdate(params).then((res) => {
				console.log('app 整包更新检测', appVersion, res)
				if (res && res.success) {
					// 检测对象是否为空
					if (Reflect.ownKeys(res.data).length === 0 || res.data.code == 'nothing') {
						if (inlet === "handcheck") {
							uni.showToast({
								title: "您使用的已是最新版本",
								icon: 'none'
							})
						} else {
							//APP整包无需更新，检查wgt包是否有更新
							checkWGTVersion(tenantId)
						}
					} else {
						let result = res.data.data
						let openUrl = result.platformUrl
						let updateType = result.updateType
						let updateVersion = result.softVersion
						if (inlet === "startcheck") {
							// 启动时检查更新
							if (notice_appVersion && notice_appVersion === updateVersion) {
								// 不再提示此版本更新
								// appUpdate(openUrl, updateType, 'app',updateVersion)
								return
							} else {
								appUpdate(openUrl, updateType, 'app', updateVersion)
							}
						} else {
							// 用户手动检查版本更新 手动检查只采取询问更新
							appUpdate(openUrl, 'ask', 'app', updateVersion)
						}
					}
				}
			}).catch((err) => {
				console.error(err);
			})
		}
	})
}

//wgt包更新
const checkWGTVersion = (tenantId) => {
	let allAppVersion = plus.runtime.version
	allAppVersion = allAppVersion.slice(0)
	let appInfo = uni.getAppBaseInfo()
	let appId = appInfo.appId
	// 获取wgt包信息
	plus.runtime.getProperty(appId, (wgtInfo) => {
		let appVersion = wgtInfo.version
		let deviceNet = ''
		// 网络类型
		uni.getNetworkType({
			success: function(res) {
				deviceNet = res.networkType
			},
			complete: function() {
				let params = {
					allAppVersion: allAppVersion,
					softName: config.softName,
					softVersion: appVersion,
					softType: 'patch',
					tenantId: tenantId,
					packName: appPackage,
					deviceType: deviceType,
					deviceId: deviceId,
					deviceIdType: '',
					deviceFirm: deviceFirm,
					deviceModel: deviceModel,
					deviceNet: deviceNet,
				}
				systemUpdate(params).then((res) => {
					if (res && res.success) {
						console.log('增量包更新', res);
						//最新版本无需更新
						if (Reflect.ownKeys(res.data).length === 0 || res.data.code ==
							'nothing') {
							return
						} else {
							let temp = res.data.data
							// 下载地址
							let openUrl = temp.platformUrl
							let updateType = temp.updateType
							appUpdate(openUrl, updateType, 'patch')
						}
					}
				})
			}
		})

	})
}

/**
 * @description: APP包下载更新方法
 * @param {string} url：下载地址
 * @param {string} updateType: 更新类型 vaule值->{force:强制更新；ask:询问更新；silence:静默更新}
 * @param {string} appType：包类型 vaule值->{app：整包；patch：增量}
 * @return {void}
 */
const appUpdate = (url, updateType, appType, version) => {
	switch (updateType) {
		case 'force':
			uni.showModal({
				title: '更新提示',
				content: '应用需要升级',
				cancelText: '取消并退出',
				confirmText: '立即升级',
				showCancel: true,
				success: function(res) {
					if (res.confirm) {
						// 清除
						kvStore.remove('notice_appVersion')
						if ('android' == deviceType) {
							uni.showLoading({
								title: '升级中…',
								mask: true,
							})
							//执行下载
							uni.downloadFile({
								url: url,
								success: (downloadResult) => {
									if (downloadResult.statusCode == 200) {
										// 安装
										plus.runtime.install(
											downloadResult.tempFilePath, {
												force: true
											},
											function(res) {
												uni.hideLoading()
												// 重启
												plus.runtime.restart()
											},
										)
									}
								},
							})
						} else {
							plus.runtime.openURL(config.iosUpdateAddress)
						}
					} else if (res.cancel) {
						// 用户点击取消本次更新 退出应用
						plus.runtime.quit()
					}
				},
			})
			break
		case 'ask':
			let content = ""
			let cancelText = ""
			if (inlet === 'startcheck') {
				content = "应用需要升级"
				cancelText = "下次再说"
			} else {
				content = "最新版本：v" + version
				cancelText = "取消"
			}
			uni.showModal({
				title: '更新提示',
				content: content,
				cancelText: cancelText,
				confirmText: '立即升级',
				success: function(res) {
					if (res.confirm) {
						kvStore.remove('notice_appVersion')
						// var platform = uni.getSystemInfoSync().osName;
						if ('android' == deviceType) {
							//用户点击确定，执行更新
							uni.showLoading({
								title: '升级中…',
								mask: true
							})
							uni.downloadFile({
								url: url,
								success: (downloadResult) => {
									if (downloadResult.statusCode == 200) {
										// 安装
										plus.runtime.install(
											downloadResult.tempFilePath, {
												force: true
											},
											function(res) {
												uni.hideLoading()
												// 重启
												if (appType == 'app') {
													plus.runtime.restart()
												} else {
													uni.showModal({
														title: '系统提示',
														content: '新版本已经更新完成，是否重启应用',
														cancelText: '取消并退出',
														confirmText: '立即重启',
														showCancel: true,
														success: function(r) {
															if (r.confirm) {
																// 用户点击确认
																plus.runtime
																	.restart()
															} else if (r
																.cancel) {
																// 用户点击取消
																plus.runtime
																	.quit()
															}
														}
													})
												}
											}
										)
									}
								}
							})
						} else {
							plus.runtime.openURL(config.iosUpdateAddress)
						}
					} else if (res.cancel) {
						// 用户点击取消本次更新
						if (inlet === "startcheck") {
							kvStore.set('notice_appVersion', version, true)
						}
						return
					}
				},
			})
			break
		case 'silence':
			if (appType == 'app') {
				// 静默更新只在wgt热更新
				return
			}
			// 获取网络状态
			uni.getNetworkType({
				success: function(res) {
					// 默认在wifi状态下进行下载
					if (res.networkType == 'wifi') {
						uni.downloadFile({
							url: url,
							success: (downloadResult) => {
								if (downloadResult.statusCode == 200) {
									// 安装
									plus.runtime.install(
										downloadResult.tempFilePath, {
											force: true
										},
										function(res) {
											// 重启后生效
											plus.runtime.restart()
										},
									)
								}
							},
						})
					} else {
						// 非wifi状态不下载
						return
					}
				}
			})
			break
	}
}