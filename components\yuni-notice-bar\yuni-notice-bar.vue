<template>
  <view>
    <u-sticky bgColor="#fff">
      <u-tabs
        :list="tabList"
        :scrollable="scrollable"
        @change="changeTab"
      ></u-tabs>
    </u-sticky>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
const props = defineProps(['scrollable', 'tabList'])
const emit = defineEmits(['changeTab'])
let scrollable = props.scrollable
let tabList = props.tabList
var tabIndex = ref(0)
function changeTab(e) {
  // console.log(e)
  emit('changeTab', e.dictValue)
}
</script>

<style lang="scss"></style>
