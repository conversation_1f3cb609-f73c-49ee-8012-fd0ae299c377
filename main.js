import App from './App'

// #ifndef VUE3
import Vue from 'vue'
Vue.config.productionTip = false
App.mpType = 'app'

try {
  function isPromise(obj) {
    return (
      !!obj &&
      (typeof obj === 'object' || typeof obj === 'function') &&
      typeof obj.then === 'function'
    )
  }

  // 统一 vue2 API Promise 化返回格式与 vue3 保持一致
  uni.addInterceptor({
    returnValue(res) {
      if (!isPromise(res)) {
        return res
      }
      return new Promise((resolve, reject) => {
        res.then((res) => {
          if (res[0]) {
            reject(res[0])
          } else {
            resolve(res[1])
          }
        })
      })
    },
  })
} catch (error) {
  console.log(JSON.stringify(error))
}

const app = new Vue({
  ...App,
})
app.$mount()


// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
import uviewPlus from '@/uni_modules/uview-plus'

import * as Pinia from 'pinia'
import {useDict } from './common/dict'
import * as globalUtils from './common/uniUtils'

// 引入路由拦截，用于限制未登录用户的访问权限
// import '@/common/interceptor.js';
// pinia持久化存储
import { createUnistorage } from './uni_modules/pinia-plugin-unistorage'
import GlobalComponents from './common/gloabComponents'
export function createApp() {
  const app = createSSRApp(App)
  const store = Pinia.createPinia()
  store.use(createUnistorage())
  app.use(store)
  app.use(uviewPlus)
  app.config.globalProperties.useDict = useDict
  // 挂载为全局方法
  app.config.globalProperties.$formatDictLabel = globalUtils.formatDictLabel

  app.use(GlobalComponents)
  return {
    app,
    Pinia,
  }
}
// #endif
