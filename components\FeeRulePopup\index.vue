<!-- components/fee-rule-popup.vue -->
<template>
    <view>
        <!-- 遮罩层 -->
        <view v-if="visible" class="popup-mask" @tap="close"></view>

        <!-- 弹窗主体 -->
        <view v-if="visible" class="popup-container">
            <!-- 标题区域 -->
            <view class="popup-header">
                <text class="popup-title">{{ title }}</text>
                <view class="close-icon" @tap="close">×</view>
            </view>

            <!-- 内容区域 (使用插槽) -->
            <view class="popup-content">
                <slot></slot>
            </view>
        </view>
    </view>

</template>

<script setup>
import { ref } from 'vue';
// import uniIcons from '@/components/uni-icons/uni-icons.vue';

const props = defineProps({
    title: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['close']);
const visible = ref(false);

// 打开弹窗
const open = () => {
    visible.value = true;
};

// 关闭弹窗
const close = () => {
    visible.value = false;
    emit('close');
};

// 暴露方法给父组件
defineExpose({ open, close });
</script>

<style scoped>
/* 遮罩层 */
.popup-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* 弹窗容器 */
.popup-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 700rpx;
    z-index: 1000;
    background: linear-gradient(to bottom, #ffffff, #ffffff);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 20rpx;
    overflow: hidden;
}

/* 标题区域 */
.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12rpx 30rpx;
    background-color: #f8f8f8;
    /* 标题背景色 */
    border-bottom: 1px solid #eee;
    /* 底部细线 */
}

.popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
}

.close-icon {
    padding: 10rpx;
}

/* 内容区域 */
.popup-content {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;
}
.close-icon {
    font-size: 50rpx;
    color: #666666;
    padding: 10rpx;
    line-height: 1;
    cursor: pointer;
}
</style>