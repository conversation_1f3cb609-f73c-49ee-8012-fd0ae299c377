/* 
 * Iconfonts 样式文件
 * 用于统一管理项目中的图标字体
 */

/* 主要图标字体 */
@font-face {
  font-family: 'iconfont';
  font-weight: normal;
  font-style: normal;
  src: url('/static/iconfont/iconfont.ttf') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 扩展图标字体 */
@font-face {
  font-family: 'iconfonts';
  font-weight: normal;
  font-style: normal;
  src: url('/static/iconfonts/iconfont.ttf') format('truetype');
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标基础样式 */
.icon {
  display: inline-block;
  font-style: normal;
  vertical-align: baseline;
  text-align: center;
  text-transform: none;
  line-height: 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标大小变体 */
.icon-xs { font-size: 12px; }
.icon-sm { font-size: 14px; }
.icon-md { font-size: 16px; }
.icon-lg { font-size: 18px; }
.icon-xl { font-size: 24px; }
.icon-xxl { font-size: 32px; }

/* 图标颜色变体 */
.icon-primary { color: #c20000; }
.icon-success { color: #4cd964; }
.icon-warning { color: #f0ad4e; }
.icon-error { color: #dd524d; }
.icon-info { color: #007aff; }
.icon-muted { color: #999; }
