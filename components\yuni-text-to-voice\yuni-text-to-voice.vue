<template>
  <view class="voice">
    <view class="textaraea">
      <textarea
        @blur="bindTextAreaBlur"
        placeholder="请输入内容"
        maxlength="200"
        focus
        auto-height
      />
    </view>
    <view class="get_voice">
      <button type="primary" size="mini" @click="toVoice1">转语音方式一</button>
      <button type="primary" size="mini" @click="toVoice2">转语音方式二</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
var textVal = ref('')
function bindTextAreaBlur(e) {
  textVal.value = e.detail.value
}
function toVoice1() {
  let msg = new SpeechSynthesisUtterance()
  msg.text = textVal.value // 待合成文字
  if (textVal.value.length == 0) {
    uni.showToast({
		title: '请输入内容',
		icon:'none'
	})
	return
  }
  msg.rate = 1 // 播放倍速
  msg.volume = 1 // 音量
  speechSynthesis.speak(msg)
}
function toVoice2() {
  let words = textVal.value
  if (words.length == 0) {
    uni.showToast({
		title: '请输入内容',
		icon:'none'
	})
	return
  }
  new Audio(
    'https://fanyi.sogou.com/reventondc/synthesis?text=' +
      words +
      '&speed=1&lang=zh-CHS&from=translateweb&speaker=6'
  ).play() // 搜狗
}
</script>

<style lang="scss" scoped>
.voice {
  width: 100%;
  .textaraea {
    width: 100%;
    margin: 20rpx;
  }
  .get_voice {
    margin-top: 300rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
}
</style>
