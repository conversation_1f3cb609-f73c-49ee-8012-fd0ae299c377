export default {
  openPage: function (options, callback) {
    const self = this
    let url = options
    if (typeof options === 'object') {
      if (options.url) {
        url = options.url
        delete options.url
      }
    } else {
      options = {}
    }
    callback = callback || options.callback
    uni.navigateTo({
      url: url,
      events: {
        //为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据,被打开页面的回调
        callbackFromOpenedPage: function (data) {
          callback && callback(data)
        },
      },
      success(res) {
        if (options.params) {
          // 通过eventChannel向被打开页面传送数据
          res.eventChannel.emit('acceptDataFromOpenerPage', options.params)
        }
      },
    })
  },
  closePage: function (options) {
    const self = this
    uni.navigateBack(options)
  },
  yu() {
    console.log('utile-yu')
  },
}
