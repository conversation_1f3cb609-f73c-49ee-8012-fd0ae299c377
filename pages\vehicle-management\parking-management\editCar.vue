<template>
  <view class="detail-container">
    <dingtalkNavBar title="修改车辆信息" :hide-nav-bar="true"></dingtalkNavBar>
    <uni-forms ref="formRef">
      <view class="form-row edit-form">
        <!-- 车牌号码 -->
        <view class="detail-item">
          <text class="detail-label">车牌号码：</text>
          <text class="detail-value">{{ formData.plateNo || '' }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label">车辆状态：</text>
          <text class="detail-value">{{
            $formatDictLabel(formData.carStatus, car_status)
          }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label must-icon">消息通知：</text>
          <text class="detail-value">
            <switch
              :checked="formData.isDing === '0'"
              @change="(e) => (formData.isDing = e.detail.value ? '0' : '1')"
              color="#c20000"
              style="transform: scale(0.7)"
            />
          </text>
        </view>

        <view class="detail-item">
          <text class="detail-label">截止日期：</text>
          <text class="detail-value">{{ formData.validDate || '' }}</text>
        </view>

        <view class="detail-item">
          <text class="detail-label must-icon">车辆品牌：</text>
          <text class="detail-value">
            <input
              class="uni-input"
              v-model="formData.carBrand"
              placeholder="请输入车辆品牌"
              :focus="true"
            />
          </text>
        </view>
        <view class="detail-item">
          <text class="detail-label must-icon">品牌型号：</text>
          <text class="detail-value">
            <input
              class="uni-input"
              v-model="formData.carModel"
              placeholder="请输入品牌型号"
              :focus="true"
            />
          </text>
        </view>
        <view class="detail-item">
          <text class="detail-label must-icon">车辆规格：</text>
          <text class="detail-value">
          
            <picker
              @change="bindPickerChange1"
              :value="vehicleSpecificationIndex"
              :range="vehicle_specification"
              range-key="label"
            >
              <view class="uni-input">{{
               vehicle_specification[vehicleSpecificationIndex].label
              }}</view>

               <text class="icon iconfont icon-jinrujiantou"></text>
            </picker>
           
          </text>
        </view>
        <view class="detail-item">
          <text class="detail-label must-icon">车辆颜色：</text>
          <text class="detail-value">
            <input
              class="uni-input"
              v-model="formData.carColor"
              placeholder="请输入车辆颜色"
              :focus="true"
            />
          </text>
        </view>
        <view class="detail-item">
          <text class="detail-label">上传图像：</text>
          <text class="detail-value upload-image">
            <imageUpload
              :uploadConfig="uploadConfig"
              @getImgUploadIds="uploadImgIds($event)"
            />
          </text>
        </view>
      </view>
    </uni-forms>

     <!-- 底部操作按钮 -->
    <view class="footer-buttons">
      <button class="cancel-btn" @click="handleCancel">取消</button>
      <button class="save-btn" @click="saveBtn">保存</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { findRecordById,updataUserCar } from '@/common/api/park/record/index'
import { onShow, onLoad } from '@dcloudio/uni-app'

const formData = ref({})
const vehicleSpecificationIndex = ref(-1)

const { proxy } = getCurrentInstance()
const { car_status, vehicle_specification } = proxy.useDict(
  'car_status',
  'vehicle_specification',
)


onLoad((options) => {
  if (options && options.item) {
    formData.value = JSON.parse(options.item)
   
     if(formData.value.vehicleSpecification){
      vehicleSpecificationIndex.value = vehicle_specification.value.findIndex(
        item => item.value === formData.value.vehicleSpecification
      )

    }
    if(formData.value.photo){
      uploadConfig.value.fileList = [
        {
          url: formData.value.photo,
          fileId: formData.value.photo,
        },
      ]
    }
  }
})

onShow(() => {})




const uploadConfig = ref({
  maxCount: 1,
  exParam: 'imgArr',
  fileList:[],
})

// 获取图片的ids
function uploadImgIds(val) {
  if (val && val.length > 0) {
    formData.value.photo = val[0].fileId
  } else {
    formData.value.photo = ''
  }
}

const bindPickerChange1 = (e) => {
  vehicleSpecificationIndex.value = e.detail.value
  formData.value.vehicleSpecification = vehicle_specification.value[e.detail.value].value
}

// ... existing script code ...

const handleCancel = () => {
  uni.navigateBack({
    delta: 1
  })
}

// ... rest of the script code ...
// 保存表单
const saveBtn = async () => {
  try {
    // 表单验证

    if (!formData.value.carBrand) {
      uni.showToast({
        title: '请输入车辆品牌',
        icon: 'none',
      })
      return
    }
    if (!formData.value.carModel) {
      uni.showToast({
        title: '请输入品牌型号',
        icon: 'none',
      })
      return
    }
       if (!formData.value.vehicleSpecification) {
      uni.showToast({
        title: '请选择车辆规格',
        icon: 'none',
      })
      return
    }
    if (!formData.value.carColor) {
      uni.showToast({
        title: '请输入车辆颜色',
        icon: 'none',
      })
      return
    }
 
    // 根据模式调用不同API
    const apiMethod = updataUserCar
const modifiedData = {
      id: formData.value.id,
      plateNo: formData.value.plateNo,
      plateColor: formData.value.plateColor,
      cellphone: formData.value.cellphone,
      carStatus: formData.value.carStatus,
      validDate: formData.value.validDate,
      carBrand: formData.value.carBrand,
      carModel: formData.value.carModel,
      carColor: formData.value.carColor,
      vehicleSpecification: formData.value.vehicleSpecification,
      photo: formData.value.photo,
      source: formData.value.source,
      vehicleType: formData.value.vehicleType,
      carType: formData.value.carType,
      orgId: formData.value.orgId,
      isDing: formData.value.isDing,
      staffId: formData.value.staffId,
      carTemporaryType: formData.value.carTemporaryType,
      tenantId: formData.value.tenantId,
      deleteFlag: 'normal'
    }

    const { code, message } = await apiMethod(formData.value)
    if (code == '1') {
      uni.showToast({
        title: '修改成功',
        icon: 'none',
        duration: 2000,
      })
      // 跳转到上一页
      
      setTimeout(() => {
          uni.$emit('carInfoUpdated')
        uni.navigateBack({
          delta: 1,
        })
      }, 1000)
    } else {
      uni.showToast({
        title: message,
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    // console.error("保存失败:", error);
    // ElMessage.error("操作失败，请稍后重试");
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/detail.scss';
:deep(.detail-label){
  width: 180rpx!important;
}
.footer-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 15px;
  background: #fff;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  
  button {
    flex: 1;
    margin: 0 10px;
    height: 44px;
    line-height: 44px;
    border-radius: 4px;
    font-size: 16px;
    
    &.cancel-btn {
      background: #f5f5f5;
      color: #333;
    }
    
    &.save-btn {
      background: #c20000;
      color: #fff;
    }
  }
}
</style>
