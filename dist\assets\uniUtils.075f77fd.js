import"./index-0e73d719.js";function n(n,t,e=[]){if(-1!=n.indexOf("?"))return n;if(null==t||"object"!=typeof t)return n;let r="";for(var i in t)if(""!==t[i]&&void 0!==t[i]&&null!==t[i])if(e.length>0){for(var o=0;o<e.length;o++)if(i==e[o]){""!=r&&(r+="&"),r+=i+"="+t[i];break}}else""!=r&&(r+="&"),r+=i+"="+t[i];return""!=r?n+"?"+r:n}function t(n){return null!=n}function e(n){const t=typeof n;return null!==n&&("object"===t||"function"===t)}function r(n){var t=new RegExp("^[ ]+$");return!(void 0!==n&&null!=n&&""!=n&&!t.test(n))}export{t as a,r as b,e as i,n as j};
