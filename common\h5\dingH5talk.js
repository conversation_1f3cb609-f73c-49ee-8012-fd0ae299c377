import * as dd from 'dingtalk-jsapi'
import {
	getJsticket
} from "@/common/api.js"
import kvStore from '@/common/store/uniKVStore.js'
// import CryptoJS from 'crypto-js'

//获取签名信息
// function getJsApiSingnature(ticket, nonce, timeStamp, url) {
// 	let plainTex = "jsapi_ticket=" + ticket + "&noncestr=" + nonce + "&timestamp=" + timeStamp + "&url=" + url;
// 	let signature = CryptoJS.SHA1(plainTex).toString();
// 	return signature;
// }
//生成签名的随机串
function generateMixed(n) {
  let res = ''
  let chars = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ]
  for (let i = 0; i < n; i++) {
    let id = Math.ceil(Math.random() * 35)
    res += chars[id]
  }
  return res
}
//签权
export function jsapiTicket(applicationId, url, callback) {
	let agentId = ""
	let corpId = ""
	let timeStamp = ""
	let nonce = ""
	let signature = ""
	if (dd.env.platform !== "notInDingTalk") {
		console.log('鉴权入参', applicationId, url,)
		getJsticket({applicationId,  url}).then(res => {
			console.log('鉴权res',res)
			const data = res.data
			agentId = data.agentId
			corpId = data.corpId
			timeStamp = data.timeStamp
			nonce = data.nonceStr
			signature = data.signature
			kvStore.set('DDH5corpId', corpId,'STR')
			kvStore.set('DDH5agentId', agentId,'STR')
			dd.config({
				agentId: agentId,
				corpId: corpId, //必填，企业ID
				timeStamp: timeStamp, // 必填，生成签名的时间戳
				nonceStr: nonce, // 必填，生成签名的随机串
				signature: signature, // 必填，签名
				jsApiList: [
					'runtime.info',
					'biz.contact.choose',
					'device.notification.confirm',
					'device.notification.alert',
					'device.notification.prompt',
					'biz.ding.post',
					'biz.util.open',
					'biz.util.openLink',
					'biz.contact.complexPicker',
					'biz.contact.departmentsPicker',
					'biz.chat.pickConversation',
					'biz.chat.chooseConversationByCorpId',
					'device.geolocation.get',
					'device.geolocation.start',
					'device.geolocation.stop',
					'biz.map.locate',
					'biz.map.search',
					'biz.map.view',
					'biz.conference.videoConfCall',
					'biz.util.datetimepicker',
					'biz.telephone.showCallMenu',
					'biz.contact.chooseMobileContacts',
					'biz.chat.toConversationByOpenConversationId',
					'biz.ding.create',
					'biz.util.uploadAttachment',
					'biz.util.uploadFile',
					'biz.util.chooseImage'
				] // 必填，需要使用的jsapi列表，注意：不要带dd。
			});
			dd.error(function(err) { //验证失败
				// console.log("进入到error中");
				// console.log(err)
				alert("钉钉签权失败，请尝试刷新页面或重新打开此应用！"+ JSON.stringify(err))
				//alert(JSON.stringify(err))
			})
			callback()
		});
	}
}

// export function isvJsapiTicket(corpId, applicationId, url, callback) {
// 	let agentId = ""
// 	let timeStamp = ""
// 	let nonce = ""
// 	let signature = ""
// 	if (dd.env.platform !== "notInDingTalk") {
// 		getISVJsticket(applicationId, corpId, url).then(res => {
// 			const data = res.data
// 			agentId = data.agentId
// 			timeStamp = data.timeStamp
// 			nonce = data.nonceStr
// 			signature = data.signature
// 			dd.config({
// 				agentId: agentId,
// 				corpId: corpId, //必填，企业ID
// 				timeStamp: timeStamp, // 必填，生成签名的时间戳
// 				nonceStr: nonce, // 必填，生成签名的随机串
// 				signature: signature, // 必填，签名
// 				jsApiList: [
// 					'runtime.info',
// 					'biz.contact.choose',
// 					'device.notification.confirm',
// 					'device.notification.alert',
// 					'device.notification.prompt',
// 					'biz.ding.post',
// 					'biz.util.openLink',
// 					'biz.contact.complexPicker',
// 					'biz.contact.departmentsPicker',
// 					'biz.chat.pickConversation',
// 					'biz.chat.chooseConversationByCorpId',
// 					'device.geolocation.get',
// 					'device.geolocation.start',
// 					'device.geolocation.stop',
// 					'biz.map.locate',
// 					'biz.map.search',
// 					'biz.map.view',
// 					'biz.conference.videoConfCall',
// 					'biz.util.datetimepicker',
// 					'biz.telephone.showCallMenu'
// 				] // 必填，需要使用的jsapi列表，注意：不要带dd。
// 			});
// 			dd.error(function(err) { //验证失败
// 				// console.log("进入到error中");
// 				// console.log(err)
// 				alert("钉钉签权失败，请尝试刷新页面或重新打开此应用！")
// 				//alert(JSON.stringify(err))
// 			})
// 			callback()
// 		});
// 	}
// }

//获取code
export function getCode(corpId, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      //使用SDK 获取免登授权码
      dd.runtime.permission.requestAuthCode({
        corpId: corpId,
        onSuccess: (info) => {
          callback(info.code)
        },
        onFail: (err) => {
          console.log(JSON.stringify(err))
        },
      })
    })
  }
}
//获取通讯录
export function getComplexPicker(
  corpId,
  agentId,
  pickedUsers,
  pickedDepartments,
  callback
) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.contact.complexPicker({
        title: '通讯录', //标题
        corpId: corpId, //企业的corpId
        multiple: true, //是否多选
        limitTips: '选择人数超出了', //超过限定人数返回提示
        maxUsers: 1000, //最大可选人数
        pickedUsers: pickedUsers, //已选用户
        pickedDepartments: pickedDepartments, //已选部门
        disabledUsers: [], //不可选用户
        disabledDepartments: [], //不可选部门
        requiredUsers: [], //必选用户（不可取消选中状态）
        requiredDepartments: [], //必选部门（不可取消选中状态）
        appId: agentId, //微应用Id，企业内部应用查看AgentId
        permissionType: 'GLOBAL', //可添加权限校验，选人权限，目前只有GLOBAL这个参数
        responseUserOnly: false, //返回人，或者返回人和部门
        startWithDepartmentId: 0, //仅支持0和-1
        onSuccess: function (result) {
          callback(result)
        },
        onFail: function (err) {
          JSON.stringify(err)
        },
      })
    })
  }
}

//获取部门
export function getDepartmentsPicker(corpId, agentId, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.contact.departmentsPicker({
        title: '测试标题', //标题
        corpId: corpId, //企业的corpId
        multiple: true, //是否多选
        limitTips: '超出了', //超过限定人数返回提示
        maxDepartments: 100, //最大选择部门数量
        pickedDepartments: [], //已选部门
        disabledDepartments: [], //不可选部门
        requiredDepartments: [], //必选部门（不可取消选中状态）
        appId: agentId, //微应用的Id
        permissionType: 'GLOBAL', //选人权限，目前只有GLOBAL这个参数
        onSuccess: function (result) {
          callback(result)
        },
        onFail: function (err) {
          // alert(JSON.stringify(err))
        },
      })
    })
  }
}

//设置导航栏标题
export function setTitle(title, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.navigation.setTitle({
        title: title, //控制标题文本，空字符串表示显示默认文本
        onSuccess: function (result) {
          /*结构
					{
					}*/
        },
        onFail: function (err) {},
      })
    })
  }
}

//设置导航栏右侧单个按钮
export function setRight(text, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.navigation.setRight({
        show: true, //控制按钮显示， true 显示， false 隐藏， 默认true
        control: true, //是否控制点击事件，true 控制，false 不控制， 默认false
        text: text, //控制显示文本，空字符串表示显示默认文本
        onSuccess: function (result) {
          callback(result)
          //如果control为true，则onSuccess将在发生按钮点击事件被回调
        },
        onFail: function (err) {},
      })
    })
  }
}

//在新窗口上打开链接
export function openLink(url, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.util.openLink({
        url: url, //要打开链接的地址
        onSuccess: function (result) {
          callback(result)
        },
        onFail: function (err) {
          console.log(JSON.stringify(err))
        },
      })
    })
  }
}

//选群组
export function pickConversation(corpId, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.chat.pickConversation({
        corpId: corpId,
        isConfirm: false,
        onSuccess: function (result) {
          callback(result)
        },
        onFail: function (err) {
          // alert(JSON.stringify(err))
        },
      })
    })
  }
}

//actionsheet
export function actionSheet(title, otherButtons, callback) {
  // if (dd.env.platform !== "notInDingTalk") {
  dd.ready(() => {
    dd.device.notification.actionSheet({
      title: title, //标题
      cancelButton: '取消', //取消按钮文本
      otherButtons: otherButtons,
      onSuccess: function (result) {
        callback(result)
        //onSuccess将在点击button之后回调
        /*{
					buttonIndex: 0 //被点击按钮的索引值，Number，从0开始, 取消按钮为-1
				}*/
      },
      onFail: function (err) {
        // alert(JSON.stringify(err))
      },
    })
  })
  // }
}

//根据corpid选择会话
export function conversationByCorpId(corpId, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.chat.chooseConversationByCorpId({
        corpId: corpId, //企业id,必须是用户所属的企业的corpid
        isAllowCreateGroup: false,
        filterNotOwnerGroup: false,
        onSuccess: function (result) {
          callback(result)
          //onSuccess将在选择结束之后调用
          /*{
						chatId: 'xxxx',
						title:'xxx'
					}*/
        },
        onFail: function (err) {},
      })
    })
  }
}

//视频会议
export function videoConfCall(title, corpId, calleeStaffIds, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.conference.videoConfCall({
        title: title,
        calleeCorpId: corpId,
        calleeStaffIds: calleeStaffIds,
        onSuccess: function (result) {
          callback(result)
        },
        onFail: function () {},
      })
    })
  }
}
//地图位置
export function locateMap(callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.map.locate({
        scope: 500, // 限制搜索POI的范围；设备位置为中心，scope为搜索半径
        onSuccess: function (result) {
          /* result 结构 */
          callback(result)
        },
        onFail: function (err) {
          // alert(JSON.stringify(err))
        },
      })
    })
  }
}

//时间选择器
export function datetimepicker(value, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.biz.util.datetimepicker({
        format: 'yyyy/MM/dd HH:mm',
        value: value, //默认显示
        onSuccess: function (result) {
          callback(result)
          //onSuccess将在点击完成之后回调
          /*{
			            value: "2015-06-10 09:50"
			        }
			        */
        },
        onFail: function (err) {},
      })
    })
  }
}
//输入框
export function plain(callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.ready(() => {
      dd.ui.input.plain({
        placeholder: '说点什么吧', //占位符
        text: '', //默认填充文本
        onSuccess: function (result) {
          callback(result)
        },
        onFail: function () {},
      })
    })
  }
}
//通用拨打电话
export function telephone(phoneNumber, code, showDingCall) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.biz.telephone.showCallMenu({
      phoneNumber: phoneNumber, // 期望拨打的电话号码
      code: code, // 国家代号，中国是+86
      showDingCall: showDingCall, // 是否显示钉钉电话
      onSuccess: function () {},
      onFail: function () {},
    })
  }
}
//页面替换
export function navReplace(url) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.biz.navigation.replace({
      url: url, // 新的页面链接
      onSuccess: function (result) {
        /*
		        {}
		        */
      },
      onFail: function (err) {},
    })
  }
}
//页面替换并回调结果
export function navReplaceResult(url, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.biz.navigation.replace({
      url: url, // 新的页面链接
      onSuccess: function (result) {
        callback(true)
        /*
		        {}
		        */
      },
      onFail: function (err) {
        callback(false)
      },
    })
  } else {
    callback(false)
  }
}
//确认框
export function dingTalkConfirm(message, title, buttonLabels, callback) {
  if (dd.env.platform !== 'notInDingTalk') {
    dd.device.notification.confirm({
      message: message,
      title: title,
      buttonLabels: buttonLabels,
      onSuccess: function (result) {
        callback(result)
        //onSuccess将在点击button之后回调
        /*
			{
				buttonIndex: 0 //被点击按钮的索引值，Number类型，从0开始
			} 
			*/
      },
      onFail: function (err) {},
    })
  }
}

export function isDingTalk() {
  if (dd.env.platform !== 'notInDingTalk') {
    return true
  }
  return false
}
