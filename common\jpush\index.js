// 开启debug模式
import {
	isDev
} from '@/common/EnvUtils.js'
import kvStore from '@/common/store/uniKVStore'
// APP极光推送
export default function jpushInit() {
	const jpushModule = uni.requireNativePlugin('JG-JPush')
	// 初始化sdk
	jpushModule.initJPushService()
	if (uni.getSystemInfoSync().platform == 'ios') {
		// 请求定位权限
		let locationServicesEnabled = jpushModule.locationServicesEnabled()
		let locationAuthorizationStatus = jpushModule.getLocationAuthorizationStatus()
		console.log('locationAuthorizationStatus', locationAuthorizationStatus)
		if (locationServicesEnabled == true && locationAuthorizationStatus < 3) {
			jpushModule.requestLocationAuthorization((result) => {
				console.log('定位权限', result.status)
			})
		}
		jpushModule.requestNotificationAuthorization((result) => {
			let status = result.status
			if (status < 2) {
				uni.showToast({
					icon: 'none',
					title: '您还没有打开通知权限',
					duration: 3000,
				})
			}
		})
	}
	jpushModule.setLoggerEnable(isDev())
	// 连接状态回调
	jpushModule.addConnectEventListener((result) => {
		console.log(result)
		let connectEnable = result.connectEnable
		uni.$emit('connectStatusChange', connectEnable)
	})
	// 通知事件回调，在手机通知栏中展示
	jpushModule.addNotificationListener((result) => {
		// console.log('通知事件回调', result)
		if (result.notificationEventType == 'notificationOpened') {
			//点击窗口通知栏推送的消息 跳转指定页面
			let notificationEventType = result.notificationEventType
			let messageID = result.messageID
			// 标题
			let title = result.title
			// 内容
			let content = result.content
			// 自定义参数（可用于完成相关业务逻辑）
			let extras = result.extras
			let tokenInfo = kvStore.get('tokenInfo',true)
			if (tokenInfo.value) {
				if (extras.url) {
					console.log('extras.url', extras.url)
					uni.navigateTo({
						url: extras.url,
					})
				} else {
					// 登录状态跳转通知页
					uni.navigateTo({
						url: '/pages/generalPage/notice/notice',
					})
				}
			} else {
				// 未登录状态跳转登录页
				// 添加收到通知消息标识
				let info = {...tokenInfo,'isNotify':true}
				kvStore.set('tokenInfo',info,true)
				uni.navigateTo({
					url: '/pages/login/login',
				})
			}
		}
	})
	// 自定义消息回调，不会显示在通知栏里
	jpushModule.addCustomMessageListener((result) => {
		let type = result.type
		let messageType = result.messageType
		let content = result.content
		uni.showToast({
			icon: 'none',
			title: JSON.stringify(result),
			duration: 3000,
		})
	})

	jpushModule.addLocalNotificationListener((result) => {
		let messageID = result.messageID
		let title = result.title
		let content = result.content
		let extras = result.extras
		uni.showToast({
			icon: 'none',
			title: JSON.stringify(result),
			duration: 3000,
		})
	})

	jpushModule.setIsAllowedInMessagePop(true)
	jpushModule.addInMessageListener((result) => {
		let eventType = result.eventType
		let messageType = result.messageType
		let content = result.content
		console.log('inMessageListener', eventType, messageType, content)

		uni.showToast({
			icon: 'none',
			title: JSON.stringify(result),
			duration: 3000,
		})
	})
}