import {  request } from '@/common/net/request.js'

import { config } from '@/config/config.js'
let apiUrl = config.apiUrl
let apiHeader = config.apiHeader
let apiHeader2 = config.apiHeader2
// 账户列表
export function pageList(params) {
  params['uniContentType'] = 'json'
    return request({
        url: `${apiHeader}/pay${apiUrl}${apiHeader2}/pay/payAccountFinancialFlows/pageList`,
        method: 'post',
        params
      })
    
}

export function detailsList(params) {
  params['uniContentType'] = 'json'
    return request({
        url: `${apiHeader}/pay${apiUrl}${apiHeader2}/pay/payAccountList/detailsList`,
        method: 'post',
        params
      })
    
}


