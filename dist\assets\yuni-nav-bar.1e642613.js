import{d as t,m as e,a,ad as l,r,b as o,o as n,c as s,w as i,n as u,g,f as c,s as f,e as d,j as h,t as b,k as _,l as p,ae as y,af as m,a5 as v,ag as I,ah as C,i as k,u as x}from"./index-4d380fda.js";import{_ as S}from"./u-status-bar.0be47f49.js";import{_ as T}from"./u-icon.66912310.js";import{_ as $}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as B}from"./u-image.5419d1e1.js";const z=$({name:"u-navbar",mixins:[e,a,{props:{safeAreaInsetTop:{type:Boolean,default:t.navbar.safeAreaInsetTop},placeholder:{type:Boolean,default:t.navbar.placeholder},fixed:{type:Boolean,default:t.navbar.fixed},border:{type:Boolean,default:t.navbar.border},leftIcon:{type:String,default:t.navbar.leftIcon},leftText:{type:String,default:t.navbar.leftText},rightText:{type:String,default:t.navbar.rightText},rightIcon:{type:String,default:t.navbar.rightIcon},title:{type:[String,Number],default:t.navbar.title},bgColor:{type:String,default:t.navbar.bgColor},titleWidth:{type:[String,Number],default:t.navbar.titleWidth},height:{type:[String,Number],default:t.navbar.height},leftIconSize:{type:[String,Number],default:t.navbar.leftIconSize},leftIconColor:{type:String,default:t.navbar.leftIconColor},autoBack:{type:Boolean,default:t.navbar.autoBack},titleStyle:{type:[String,Object],default:t.navbar.titleStyle}}}],data:()=>({}),emits:["leftClick","rightClick"],methods:{leftClick(){this.$emit("leftClick"),this.autoBack&&l()},rightClick(){this.$emit("rightClick")}}},[["render",function(t,e,a,l,y,m){const v=_,I=r(o("u-status-bar"),S),C=r(o("u-icon"),T),k=p;return n(),s(v,{class:"u-navbar"},{default:i((()=>[t.fixed&&t.placeholder?(n(),s(v,{key:0,class:"u-navbar__placeholder",style:u({height:t.$u.addUnit(t.$u.getPx(t.height)+t.$u.sys().statusBarHeight,"px")})},null,8,["style"])):g("",!0),c(v,{class:f([t.fixed&&"u-navbar--fixed"])},{default:i((()=>[t.safeAreaInsetTop?(n(),s(I,{key:0,bgColor:t.bgColor},null,8,["bgColor"])):g("",!0),c(v,{class:f(["u-navbar__content",[t.border&&"u-border-bottom"]]),style:u({height:t.$u.addUnit(t.height),backgroundColor:t.bgColor})},{default:i((()=>[c(v,{class:"u-navbar__content__left","hover-class":"u-navbar__content__left--hover","hover-start-time":"150",onClick:m.leftClick},{default:i((()=>[d(t.$slots,"left",{},(()=>[t.leftIcon?(n(),s(C,{key:0,name:t.leftIcon,size:t.leftIconSize,color:t.leftIconColor},null,8,["name","size","color"])):g("",!0),t.leftText?(n(),s(k,{key:1,style:u({color:t.leftIconColor}),class:"u-navbar__content__left__text"},{default:i((()=>[h(b(t.leftText),1)])),_:1},8,["style"])):g("",!0)]),!0)])),_:3},8,["onClick"]),d(t.$slots,"center",{},(()=>[c(k,{class:"u-line-1 u-navbar__content__title",style:u([{width:t.$u.addUnit(t.titleWidth)},t.$u.addStyle(t.titleStyle)])},{default:i((()=>[h(b(t.title),1)])),_:1},8,["style"])]),!0),t.$slots.right||t.rightIcon||t.rightText?(n(),s(v,{key:0,class:"u-navbar__content__right",onClick:m.rightClick},{default:i((()=>[d(t.$slots,"right",{},(()=>[t.rightIcon?(n(),s(C,{key:0,name:t.rightIcon,size:"20"},null,8,["name"])):g("",!0),t.rightText?(n(),s(k,{key:1,class:"u-navbar__content__right__text"},{default:i((()=>[h(b(t.rightText),1)])),_:1})):g("",!0)]),!0)])),_:3},8,["onClick"])):g("",!0)])),_:3},8,["class","style"])])),_:3},8,["class"])])),_:3})}],["__scopeId","data-v-7df887bc"]]),N=$({__name:"yuni-nav-bar",props:{leftTextColor:{type:String,default:"#000"},leftTextSize:{type:Number,default:16},rightTextColor:{type:String,default:"#000"},rightTextSize:{type:Number,default:16},bgImageUrl:{type:String,default:""},isFullScreen:{type:Boolean,default:!1},bgImageHeight:{type:Number,default:0}},emits:["handlerLeft","handlerRight"],setup(t,{emit:e}){const a=t,l=e,f=()=>{l("handlerLeft")},h=()=>{l("handlerRight")},b=y();let p=2*(m().statusBarHeight+(b.height?Number(b.height):44));const S=v((()=>0==a.bgImageHeight?p:2*a.bgImageHeight));return(e,a)=>{const l=r(o("u-navbar"),z),b=r(o("u-image"),B),y=_;return n(),s(y,{class:"nav-container",style:u({height:x(p)+"rpx"})},{default:i((()=>[c(l,I({safeAreaInsetTop:"",bgColor:t.bgImageUrl?"rgba(0, 0, 0, 0)":e.$attrs.bgColor},e.$attrs,{autoBack:!0,onLeftClick:f,onRightClick:h}),C({_:2},[k(e.$slots,((t,a)=>({name:a,fn:i((()=>[d(e.$slots,a,{},void 0,!0)]))})))]),1040,["bgColor"]),t.bgImageUrl?(n(),s(y,{key:0,class:"nav-backImage"},{default:i((()=>[c(b,{width:"100%",mode:"top",height:t.isFullScreen?"100vh":S.value+"rpx",src:t.bgImageUrl},null,8,["height","src"])])),_:1})):g("",!0)])),_:3},8,["style"])}}},[["__scopeId","data-v-123addea"]]);export{N as _};
