import { request } from '../request.js'
import {
  WECHAT_CALLBACKMP_URL,
  WECHAT_GETPHONENUMBER_URL,
  WECHAT_WECHATBINDMP_URL,
} from '@/common/net/netUrl.js'

// 回调接口
export function callbackMp(params) {
  console.log(params)
  return request({
    url: WECHAT_CALLBACKMP_URL,
    method: 'GET',
    params,
  })
}
// 获取手机号
export function getPhoneNumber(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: WECHAT_GETPHONENUMBER_URL,
    method: 'POST',
    params,
  })
}
// 绑定手机号
export function wechatMpBindMp(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: WECHAT_WECHATBINDMP_URL,
    method: 'POST',
    params,
  })
}
