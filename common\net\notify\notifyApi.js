import { request } from '../request.js'
import { isDef } from '@/common/uniUtils.js'
import {
  NOTIFY_TOP5PAGE_URL,
  NOTIFY_Tab_URL,
  NOTIFY_LIST_URL,
  NOTIFY_DETAILS_URL,
} from '@/common/net/netUrl.js'
// 首页公告请求接口
export function selectTop5Page(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: NOTIFY_TOP5PAGE_URL,
    method: 'POST',
    params,
  })
}

//公告tab标题接口
export function selectNotifyTab(params, header) {
  if (!isDef(params)) {
    params = {}
  }
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: NOTIFY_Tab_URL,
    method: 'GET',
    params,
    header,
  })
}

// 公告列表接口
export function selectForListPage(params, header) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: NOTIFY_LIST_URL,
    method: 'POST',
    params,
    header,
  })
}

// 公告详情接口
// parmas示例：
// {
// 	noticeId:"ddddsafsaf"
// }
export function selectNoticeDetails(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: NOTIFY_DETAILS_URL + '?noticeId=' + params.noticeId,
    params,
    method: 'POST',
  })
}
