{
    "name" : "智慧园区平台",
    "appid" : "__UNI__98A5010",
    "description" : "智慧后勤",
    "versionName" : "1.0.2",
    "versionCode" : 1,
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "privacy" : {
            "prompt" : "template", //可取值template、none
            "template" : {
                "title" : "服务协议和隐私政策",
                "message" : "　　请你务必审慎阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了更好的向你提供服务，我们需要收集你的设备标识、操作日志等信息用于分析、优化应用性能。<br/>　　你可阅读<a href=\"\">《服务协议》</a>和<a href=\"\">《隐私政策》</a>了解详细信息。如果你同意，请点击下面按钮开始接受我们的服务。",
                "buttonAccept" : "同意",
                "buttonRefuse" : "暂不同意",
                "second" : {
                    "title" : "温馨提示",
                    "message" : "　　进入应用前，你需先同意<a href=\"\">《服务协议》</a>和<a href=\"\">《隐私政策》</a>，否则将退出应用。",
                    "buttonAccept" : "同意并继续",
                    "buttonRefuse" : "退出应用"
                }
            }
        },
        /* 模块配置 */
        "modules" : {
            "Barcode" : {},
            "Bluetooth" : {},
            "Camera" : {},
            "Contacts" : {},
            "FaceID" : {},
            "Fingerprint" : {},
            "Messaging" : {},
            "Record" : {},
            "Maps" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    "<uses-permission android:name=\"android.permission.SEND_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.USE_FINGERPRINT\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SMS\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_USER_PRESENT\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "minSdkVersion" : 21,
                "excludePermissions" : []
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false,
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "提交相册中的图片",
                    "NSCameraUsageDescription" : "拍摄照片进行上传",
                    "NSLocationWhenInUseUsageDescription" : "运行期间访问地理位置"
                }
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "geolocation" : {},
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "",
                        "UniversalLinks" : ""
                    }
                },
                "oauth" : {
                    "weixin" : {
                        "appid" : "",
                        "UniversalLinks" : ""
                    }
                },
                "statics" : {
                    "umeng" : {
                        "appkey_ios" : "",
                        "channelid_ios" : "",
                        "appkey_android" : "638fe65188ccdf4b7e9cc160",
                        "channelid_android" : "unidoor"
                    }
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true,
                "androidStyle" : "default",
                "android" : {
                    "hdpi" : "static/app-plus/welcome480.9.png",
                    "xhdpi" : "static/app-plus/welcome720.9.png",
                    "xxhdpi" : "static/app-plus/welcome1080.9.png"
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "static/app-plus/Android72.png",
                    "xhdpi" : "static/app-plus/Android96.png",
                    "xxhdpi" : "static/app-plus/Android144.png",
                    "xxxhdpi" : "static/app-plus/Android192.png"
                },
                "ios" : {
                    "appstore" : "static/app-plus/iOS1024-1x.png",
                    "iphone" : {
                        "app@2x" : "static/app-plus/iOS120.png",
                        "app@3x" : "static/app-plus/iOS180.png",
                        "spotlight@2x" : "static/app-plus/iOS80.png",
                        "spotlight@3x" : "static/app-plus/iOS120.png",
                        "settings@2x" : "static/app-plus/iOS58.png",
                        "settings@3x" : "static/app-plus/iOS87.png",
                        "notification@2x" : "static/app-plus/iOS40.png",
                        "notification@3x" : "static/app-plus/iOS60.png"
                    }
                }
            }
        },
        "nativePlugins" : {
            "JG-JCore" : {
                "JPUSH_APPKEY_IOS" : "36413eea1b10edbfead55192",
                "JPUSH_CHANNEL_IOS" : "36413eea1b10edbfead55192",
                "JPUSH_APPKEY_ANDROID" : "36413eea1b10edbfead55192",
                "JPUSH_CHANNEL_ANDROID" : "36413eea1b10edbfead55192",
                "__plugin_info__" : {
                    "name" : "JG-JCore",
                    "description" : "极光推送JCore插件",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "com.uni.door",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {
                        "JPUSH_APPKEY_IOS" : {
                            "des" : "[iOS]极光portal配置应用信息时分配的AppKey",
                            "key" : "JCore:APP_KEY",
                            "value" : ""
                        },
                        "JPUSH_CHANNEL_IOS" : {
                            "des" : "[iOS]用于统计分发渠道，不需要可填默认值developer-default",
                            "key" : "JCore:CHANNEL",
                            "value" : ""
                        },
                        "JPUSH_APPKEY_ANDROID" : {
                            "des" : "[Android]极光portal配置应用信息时分配的AppKey",
                            "key" : "JPUSH_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_CHANNEL_ANDROID" : {
                            "des" : "[Android]用于统计分发渠道，不需要可填默认值developer-default",
                            "key" : "JPUSH_CHANNEL",
                            "value" : ""
                        }
                    }
                }
            },
            "JG-JPush" : {
                "JPUSH_ISPRODUCTION_IOS" : "",
                "JPUSH_ADVERTISINGID_IOS" : "",
                "JPUSH_DEFAULTINITJPUSH_IOS" : "",
                "JPUSH_OPPO_APPKEY" : "",
                "JPUSH_OPPO_APPID" : "",
                "JPUSH_OPPO_APPSECRET" : "",
                "JPUSH_VIVO_APPKEY" : "",
                "JPUSH_VIVO_APPID" : "",
                "JPUSH_MEIZU_APPKEY" : "",
                "JPUSH_MEIZU_APPID" : "",
                "JPUSH_XIAOMI_APPKEY" : "",
                "JPUSH_XIAOMI_APPID" : "",
                "JPUSH_HUAWEI_APPID" : "",
                "__plugin_info__" : {
                    "name" : "JG-JPush",
                    "description" : "极光推送Hbuilder插件",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "com.uni.door",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {
                        "JPUSH_ISPRODUCTION_IOS" : {
                            "des" : "[iOS]是否是生产环境，是填true，不是填false或者不填",
                            "key" : "JPush:ISPRODUCTION",
                            "value" : ""
                        },
                        "JPUSH_ADVERTISINGID_IOS" : {
                            "des" : "[iOS]广告标识符（IDFA）如果不需要使用IDFA，可不填",
                            "key" : "JPush:ADVERTISINGID",
                            "value" : ""
                        },
                        "JPUSH_DEFAULTINITJPUSH_IOS" : {
                            "des" : "[iOS]是否默认初始化，是填true，不是填false或者不填",
                            "key" : "JPush:DEFAULTINITJPUSH",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPKEY" : {
                            "des" : "厂商OPPO-appkey,示例：OP-12345678",
                            "key" : "OPPO_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPID" : {
                            "des" : "厂商OPPO-appId,示例：OP-12345678",
                            "key" : "OPPO_APPID",
                            "value" : ""
                        },
                        "JPUSH_OPPO_APPSECRET" : {
                            "des" : "厂商OPPO-appSecret,示例：OP-12345678",
                            "key" : "OPPO_APPSECRET",
                            "value" : ""
                        },
                        "JPUSH_VIVO_APPKEY" : {
                            "des" : "厂商VIVO-appkey,示例：12345678",
                            "key" : "com.vivo.push.api_key",
                            "value" : ""
                        },
                        "JPUSH_VIVO_APPID" : {
                            "des" : "厂商VIVO-appId,示例：12345678",
                            "key" : "com.vivo.push.app_id",
                            "value" : ""
                        },
                        "JPUSH_MEIZU_APPKEY" : {
                            "des" : "厂商MEIZU-appKey,示例：MZ-12345678",
                            "key" : "MEIZU_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_MEIZU_APPID" : {
                            "des" : "厂商MEIZU-appId,示例：MZ-12345678",
                            "key" : "MEIZU_APPID",
                            "value" : ""
                        },
                        "JPUSH_XIAOMI_APPKEY" : {
                            "des" : "厂商XIAOMI-appKey,示例：MI-12345678",
                            "key" : "XIAOMI_APPKEY",
                            "value" : ""
                        },
                        "JPUSH_XIAOMI_APPID" : {
                            "des" : "厂商XIAOMI-appId,示例：MI-12345678",
                            "key" : "XIAOMI_APPID",
                            "value" : ""
                        },
                        "JPUSH_HUAWEI_APPID" : {
                            "des" : "厂商HUAWEI-appId,示例：appid=12346578",
                            "key" : "com.huawei.hms.client.appid",
                            "value" : ""
                        }
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxa83f7ddb7b916d62",
        "setting" : {
            "urlCheck" : false,
            "minified" : true
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "演示定位能力"
            }
        },
        "plugins" : {
            "routePlan" : {
                "version" : "1.0.19",
                "provider" : "wx50b5593e81dd937a"
            }
        },
        "requiredPrivateInfos" : [ "getLocation" ]
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "h5" : {
        "router" : {
            "mode" : "history",
            "base" : "/sdlt-zhyq-dd/"
        },
        "devServer" : {
            "https" : false
        },
        "sdkConfigs" : {
            "maps" : {
                "amap" : {
                    "key" : "2588f9a78ee57e0d9bd2395665984f12",
                    "securityJsCode" : "d4c7f0e24a49f8e0b84f68a2e652816b",
                    "serviceHost" : ""
                }
            }
        },
        "template" : "",
        "title" : "智慧园区"
    }
}
