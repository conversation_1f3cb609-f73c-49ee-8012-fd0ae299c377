import { request } from '../request.js'
import { isObj, isDef } from '../../uniUtils.js'
import { ANNOUCE_TOPPAGE_URL } from '@/common/net/netUrl.js'
//顶部位置轮播广告请求
export function selectTopPageCarousel_UP(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['advertisingType'] = 'carousel'
    params['site'] = 'up'
  }
  selectTopPage(params)
}
//中部位置轮播广告请求
export function selectTopPageCarousel_Middle(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['advertisingType'] = 'carousel'
    params['site'] = 'middle'
  }
  selectTopPage(params)
}
//底部位置轮播广告请求
export function selectTopPageCarousel_Down(params) {
  if (!isDef(params)) {
    params = {}
  }
  // if(isObj(params)){
  // 	params["advertisingType"] = "carousel" ;
  // 	params["site"] = "down" ;
  // }
  return selectTopPage(params)
}
//顶部位置普通广告请求
export function selectTopPageNormal_UP(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['advertisingType'] = 'general'
    params['site'] = 'up'
  }
  selectTopPage(params)
}
//中部位置普通广告请求
export function selectTopPageNormal_Middle(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['advertisingType'] = 'general'
    params['site'] = 'middle'
  }
  selectTopPage(params)
}
//底部位置普通广告请求
export function selectTopPageNormal_Down(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['advertisingType'] = 'general'
    params['site'] = 'down'
  }
  selectTopPage(params)
}

//顶部位置文本广告请求
export function selectTopPageText_UP(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['advertisingType'] = 'text'
    params['site'] = 'up'
  }
  selectTopPage(params)
}
//中部位置文本广告请求
export function selectTopPageText_Middle(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['advertisingType'] = 'text'
    params['site'] = 'middle'
  }
  selectTopPage(params)
}
//底部位置文本广告请求
export function selectTopPageText_Down(params) {
  if (!isDef(params)) {
    params = {}
  }
  if (isObj(params)) {
    params['advertisingType'] = 'text'
    params['site'] = 'down'
  }
  selectTopPage(params)
}

function selectTopPage(params) {
  if (isObj(params)) {
    params['uniContentType'] = 'json'
    params['queryNum'] = 5
    params['start'] = 1 //预留字段，分页从下表从1开始；
  }
  console.log(params)
  return request({
    url: ANNOUCE_TOPPAGE_URL,
    method: 'POST',
    params,
  })
}
