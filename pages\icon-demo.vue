<template>
  <view class="demo-page">
    <view class="demo-header">
      <text class="demo-title">图标使用示例</text>
    </view>
    
    <view class="demo-content">
      <!-- 基础使用方法 -->
      <view class="demo-section">
        <text class="demo-subtitle">基础使用</text>
        <view class="demo-row">
          <text class="demo-label">右箭头:</text>
          <text class="iconfont icon-arrow-right demo-icon"></text>
        </view>
        <view class="demo-row">
          <text class="demo-label">日历:</text>
          <text class="iconfont icon-calendar demo-icon"></text>
        </view>
        <view class="demo-row">
          <text class="demo-label">我的车辆:</text>
          <text class="iconfonts icon-wodecheliang demo-icon"></text>
        </view>
      </view>
      
      <!-- 不同大小 -->
      <view class="demo-section">
        <text class="demo-subtitle">不同大小</text>
        <view class="size-demo">
          <text class="iconfont icon-arrow-right" style="font-size: 24rpx; color: #666;"></text>
          <text class="iconfont icon-arrow-right" style="font-size: 32rpx; color: #666;"></text>
          <text class="iconfont icon-arrow-right" style="font-size: 48rpx; color: #c20000;"></text>
          <text class="iconfont icon-arrow-right" style="font-size: 64rpx; color: #c20000;"></text>
        </view>
      </view>
      
      <!-- 使用说明 -->
      <view class="demo-section">
        <text class="demo-subtitle">使用说明</text>
        <view class="usage-info">
          <text class="usage-text">1. 添加 iconfont 或 iconfonts 类名</text>
          <text class="usage-text">2. 添加具体的图标类名，如 icon-arrow-right</text>
          <text class="usage-text">3. 可以通过 style 设置大小和颜色</text>
        </view>
      </view>
      
      <!-- 代码示例 -->
      <view class="demo-section">
        <text class="demo-subtitle">代码示例</text>
        <view class="code-example">
          <text class="code-text">&lt;text class="iconfont icon-arrow-right"&gt;&lt;/text&gt;</text>
          <text class="code-text">&lt;text class="iconfonts icon-wodecheliang"&gt;&lt;/text&gt;</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('图标示例页面加载完成')
})
</script>

<style lang="scss">
/* 引入字体文件 */
@import '@/static/iconfont/iconfont.css';
@import '@/static/iconfonts/iconfont.css';

.demo-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.demo-content {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.demo-section {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.demo-subtitle {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.demo-row {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  padding: 15rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.demo-label {
  width: 200rpx;
  font-size: 28rpx;
  color: #666;
}

.demo-icon {
  font-size: 48rpx;
  color: #c20000;
}

.size-demo {
  display: flex;
  align-items: center;
  gap: 30rpx;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

.usage-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.usage-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  padding: 10rpx 0;
}

.code-example {
  background: #f0f0f0;
  border-radius: 8rpx;
  padding: 20rpx;
  border-left: 6rpx solid #c20000;
}

.code-text {
  display: block;
  font-family: 'Courier New', monospace;
  font-size: 24rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 10rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

/* 确保图标字体正确应用 */
.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal !important;
  font-weight: normal !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  vertical-align: middle;
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-style: normal !important;
  font-weight: normal !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  vertical-align: middle;
}

/* 图标定义 */
.icon-arrow-right:before { content: '\e665'; }
.icon-calendar:before { content: '\e667'; }
.icon-wodecheliang:before { content: "\e615"; }
</style>
