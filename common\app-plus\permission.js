/// null = 未请求，1 = 已允许，0 = 拒绝|受限, 2 = 系统未开启

var isIOS
// #ifdef APP-PLUS
isIOS = plus.os.name == 'iOS'
// #endif

// 判断相册权限是否开启
function album() {
  var result = 0
  var PHPhotoLibrary = plus.ios.import('PhotoLibrary')
  var authStatus = PHPhotoLibrary.authorizationStatus()
  if (authStatus === 0) {
    result = null
  } else if (authStatus == 3) {
    result = 1
    console.log('相册权限已经开启')
  } else {
    result = 0
  }
  plus.ios.deleteObject(PHPhotoLibrary)
  return result
}

// 判断相机权限是否开启
function camera() {
  var result = 0
  var AVCaptureDevice = plus.ios.import('AVCaptureDevice')
  var authStatus = AVCaptureDevice.authorizationStatusForMediaType('vide')
  console.log('相机权限',authStatus);
  if (authStatus === 0) {
    result = null
  } else if (authStatus == 3) {
    result = 1
    console.log('相机权限已经开启')
  } else {
    result = 0
  }
  plus.ios.deleteObject(AVCaptureDevice)
  return result
}

// 判断定位权限是否开启
function location() {
  var result = 0
  var cllocationManger = plus.ios.import('CLLocationManager')
  var enable = cllocationManger.locationServicesEnabled()
  var status = cllocationManger.authorizationStatus()
  if (!enable) {
    result = 2
  } else if (status === 0) {
    result = null
  } else if (status === 3 || status === 4) {
    result = 1
  } else {
    result = 0
  }
  plus.ios.deleteObject(cllocationManger)
  return result
}

// 判断推送权限是否开启
function push() {
  var result = 0
  var UIApplication = plus.ios.import('UIApplication')
  var app = UIApplication.sharedApplication()
  var enabledTypes = 0
  if (app.currentUserNotificationSettings) {
    var settings = app.currentUserNotificationSettings()
    enabledTypes = settings.plusGetAttribute('types')
    if (enabledTypes == 0) {
      result = 0
      console.log('推送权限没有开启')
    } else {
      result = 1
      console.log('已经开启推送功能!')
    }
    plus.ios.deleteObject(settings)
  } else {
    enabledTypes = app.enabledRemoteNotificationTypes()
    if (enabledTypes == 0) {
      result = 3
      console.log('推送权限没有开启!')
    } else {
      result = 4
      console.log('已经开启推送功能!')
    }
  }
  plus.ios.deleteObject(app)
  plus.ios.deleteObject(UIApplication)
  return result
}

// 判断通讯录权限是否开启
function contact() {
  var result = 0
  var CNContactStore = plus.ios.import('CNContactStore')
  var cnAuthStatus = CNContactStore.authorizationStatusForEntityType(0)
  if (cnAuthStatus === 0) {
    result = null
  } else if (cnAuthStatus == 3) {
    result = 1
    console.log('通讯录权限已经开启')
  } else {
    result = 0
  }
  plus.ios.deleteObject(CNContactStore)
  return result
}

// 判断麦克风权限是否开启
function record() {
  var result = null
  var avaudiosession = plus.ios.import('AVAudioSession')
  var avaudio = avaudiosession.sharedInstance()
  var status = avaudio.recordPermission()
  console.log('permissionStatus:' + status)
  if (status === 1970168948) {
    result = null
  } else if (status === 1735552628) {
    result = 1
    console.log('麦克风权限已经开启')
  } else {
    result = 0
  }
  plus.ios.deleteObject(avaudiosession)
  return result
}

// 判断日历权限是否开启
function calendar() {
  var result = null
  var EKEventStore = plus.ios.import('EKEventStore')
  var ekAuthStatus = EKEventStore.authorizationStatusForEntityType(0)
  if (ekAuthStatus == 3) {
    result = 1
    console.log('日历权限已经开启')
  } else {
    console.log('日历权限没有开启')
  }
  plus.ios.deleteObject(EKEventStore)
  return result
}

// 判断备忘录权限是否开启
function memo() {
  var result = null
  var EKEventStore = plus.ios.import('EKEventStore')
  var ekAuthStatus = EKEventStore.authorizationStatusForEntityType(1)
  if (ekAuthStatus == 3) {
    result = 1
    console.log('备忘录权限已经开启')
  } else {
    console.log('备忘录权限没有开启')
  }
  plus.ios.deleteObject(EKEventStore)
  return result
}

function requestIOS(permissionID) {
  return new Promise((resolve, reject) => {
    switch (permissionID) {
      case 'push':
        resolve(push())
        break
      case 'location':
        resolve(location())
        break
      case 'record':
        resolve(record())
        break
      case 'camera':
        resolve(camera())
        break
      case 'album':
        resolve(album())
        break
      case 'contact':
        resolve(contact())
        break
      case 'calendar':
        resolve(calendar())
        break
      case 'memo':
        resolve(memo())
        break
      default:
        resolve(0)
        break
    }
  })
}

// Android权限查询
function requestAndroid(permissionID) {
  return new Promise((resolve, reject) => {
    plus.android.requestPermissions(
      [permissionID],
      function (resultObj) {
        var result = 0
        for (var i = 0; i < resultObj.granted.length; i++) {
          var grantedPermission = resultObj.granted[i]
          console.log('已获取的权限：' + grantedPermission)
          result = 1
        }
        for (var i = 0; i < resultObj.deniedPresent.length; i++) {
          var deniedPresentPermission = resultObj.deniedPresent[i]
          console.log('拒绝本次申请的权限：' + deniedPresentPermission)
          result = 0
        }
        for (var i = 0; i < resultObj.deniedAlways.length; i++) {
          var deniedAlwaysPermission = resultObj.deniedAlways[i]
          console.log('永久拒绝申请的权限：' + deniedAlwaysPermission)
          result = -1
        }
        resolve(result)
        // if(result != 1){
        //   gotoAppPermissionSetting()
        // }
      },
      function (error) {
        console.log('申请权限错误：' + error.code + ' = ' + error.message)
        resolve({
          code: error.code,
          message: error.message,
        })
      }
    )
  })
}

function requestAndroidPermission(permissionID, permissionName) {
  return new Promise((resolve, reject) => {
    plus.android.requestPermissions(
      [permissionID], // 理论上支持多个权限同时查询，但实际上本函数封装只处理了一个权限的情况。有需要的可自行扩展封装
      function (resultObj) {
        var result = 0
        for (var i = 0; i < resultObj.granted.length; i++) {
          var grantedPermission = resultObj.granted[i]
          console.log('已获取的权限：' + grantedPermission)
          result = 1
        }
        for (var i = 0; i < resultObj.deniedPresent.length; i++) {
          var deniedPresentPermission = resultObj.deniedPresent[i]
          console.log('拒绝本次申请的权限：' + deniedPresentPermission)
          result = 0
        }
        for (var i = 0; i < resultObj.deniedAlways.length; i++) {
          var deniedAlwaysPermission = resultObj.deniedAlways[i]
          console.log('永久拒绝申请的权限：' + deniedAlwaysPermission)
          result = -1
        }
        resolve({
          result: result,
          permissionName: permissionName,
        })
        // 若所需权限被拒绝,则打开APP设置界面,可以在APP设置界面打开相应权限
        // if (result != 1) {
        // gotoAppPermissionSetting()
        // }
      },
      function (error) {
        console.log('申请权限错误：' + error.code + ' = ' + error.message)
        resolve({
          code: error.code,
          message: error.message,
        })
      }
    )
  })
}

// 使用一个方法，根据参数判断权限
function judgePermission(permissionID, callback) {
  function handle(res) {
    callback && callback(res.result)
	let content = ''
	switch(permissionID){
		case 'camera':
		content = '为了正常使用拍照服务，图片上传，图片识别服务，请允许使用摄像头权限。你可以通过系统“设置”进行权限的管理';
		break
		case 'call_phone':
		content = '为了正常拨打电话，读取电话状态，短信验证服务，请允许使用拨打电话权限。你可以通过系统“设置”进行权限的管理';
		break
		case 'record':
		content = '为了正常录制音频、语音识别等功能，请允许使用录音权限。你可以通过系统“设置”进行权限的管理';
		break
		case 'send_sms':
		content = '为了正常发送短信、读取短信等功能，请允许使用短信权限。你可以通过系统“设置”进行权限的管理';
		break
		case 'photoLibrary':
		content = '为了正常使用图片上传、图片识别服务，请允许使用存储权限。你可以通过系统“设置”进行权限的管理';
		break
		default:
		content = '为了正常的功能使用，系统需要' + res.permissionName + '权限。你可以通过系统“设置”进行权限的管理'
		break
	}
    if (res.result === -1) {
      uni.showModal({
        title: '权限申请',
        content:content,
        confirmText: '继续',
        cancelText: '关闭',
        success: (data) => {
          if (data.confirm) {
            gotoAppPermissionSetting()
          }
        },
      })
    }
  }
  if (permissionID == 'location') {
    // 位置
    if (isIOS) {
      handle(judgeIosPermissionLocation())
    } else {
      requestAndroidPermission(
        'android.permission.ACCESS_FINE_LOCATION',
        '位置'
      ).then(handle)
    }
  } else if (permissionID == 'camera') {
    // 摄像头
    if (isIOS) {
      handle(judgeIosPermissionCamera())
    } else {
      requestAndroidPermission('android.permission.CAMERA', '摄像头').then(
        handle
      )
    }
  } else if (permissionID == 'photoLibrary') {
    // 相册
    if (isIOS) {
      handle(judgeIosPermissionPhotoLibrary())
    } else {
      requestAndroidPermission(
        'android.permission.READ_EXTERNAL_STORAGE',
        '相册读取'
      ).then(handle)
    }
  } else if (permissionID == 'record') {
    // 麦克风
    if (isIOS) {
      handle(judgeIosPermissionRecord())
    } else {
      requestAndroidPermission(
        'android.permission.RECORD_AUDIO',
        '麦克风'
      ).then(handle)
    }
  } else if (permissionID == 'push') {
    // 推送
    if (isIos) {
      handle(judgeIosPermissionPush())
    } else {
      handle(1)
    }
  } else if (permissionID == 'contact') {
    // 通讯录
    if (isIOS) {
      handle(judgeIosPermissionContact())
    } else {
      requestAndroidPermission(
        'android.permission.READ_CONTACTS',
        '通讯录读取'
      ).then(handle)
    }
  } else if (permissionID == 'calendar') {
    // 日历
    if (isIOS) {
      handle(judgeIosPermissionCalendar())
    } else {
      requestAndroidPermission(
        'android.permission.READ_CALENDAR',
        '日历读取'
      ).then(handle)
    }
  } else if (permissionID == 'memo') {
    // 备忘录
    if (isIOS) {
      handle(judgeIosPermissionMemo())
    } else {
      handle(1)
    }
  } else if (permissionID == 'call_phone') {
    // 拨打电话
    if (isIOS) {
      handle(1)
    } else {
      requestAndroidPermission(
        'android.permission.CALL_PHONE',
        '拨打电话'
      ).then(handle)
    }
  } else if(permissionID == 'send_sms'){
	  //发送短信
	  if (isIOS) {
	    handle(1)
	  } else {
	    requestAndroidPermission(
	      'android.permission.SEND_SMS',
	      '发送短信'
	    ).then(handle)
	  }
  }
}

// 跳转到**应用**的权限页面
function gotoAppPermissionSetting() {
  if (permission.isIOS) {
    var UIApplication = plus.ios.import('UIApplication')
    var application2 = UIApplication.sharedApplication()
    var NSURL2 = plus.ios.import('NSURL')
    var setting2 = NSURL2.URLWithString('app-settings:')
    application2.openURL(setting2)
    plus.ios.deleteObject(setting2)
    plus.ios.deleteObject(NSURL2)
    plus.ios.deleteObject(application2)
  } else {
    var Intent = plus.android.importClass('android.content.Intent')
    var Settings = plus.android.importClass('android.provider.Settings')
    var Uri = plus.android.importClass('android.net.Uri')
    var mainActivity = plus.android.runtimeMainActivity()
    var intent = new Intent()
    intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
    var uri = Uri.fromParts('package', mainActivity.getPackageName(), null)
    intent.setData(uri)
    mainActivity.startActivity(intent)
  }
}

// 检查系统的设备服务是否开启
// var checkSystemEnableLocation = async function () {
function checkSystemEnableLocation() {
  if (isIOS) {
    var result = false
    var cllocationManger = plus.ios.import('CLLocationManager')
    var result = cllocationManger.locationServicesEnabled()
    console.log('系统定位开启:' + result)
    plus.ios.deleteObject(cllocationManger)
    return result
  } else {
    var context = plus.android.importClass('android.content.Context')
    var locationManager = plus.android.importClass(
      'android.location.LocationManager'
    )
    var main = plus.android.runtimeMainActivity()
    var mainSvr = main.getSystemService(context.LOCATION_SERVICE)
    var result = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER)
    console.log('系统定位开启:' + result)
    return result
  }
}

const permission = {
  get isIOS() {
    return typeof isIOS === 'boolean'
      ? isIOS
      : (isIOS = uni.getSystemInfoSync().platform === 'ios')
  },
  requestIOS: requestIOS,
  requestAndroid: requestAndroid,
  requestAndroidPermission: requestAndroidPermission,
  gotoAppSetting: gotoAppPermissionSetting,
  judgePermission: judgePermission,
  checkSystemEnableLocation: checkSystemEnableLocation,
  iscamera:camera
}

export default permission
