<template>
  <view class="page-box">
    <dingtalkNavBar title="停车管理" :hide-nav-bar="true"></dingtalkNavBar>
    <view class="park-box" style="padding-bottom: 0px">
      <view class="title" style="margin-bottom: 20rpx">
        <text class="icon iconfonts icon-tingchechang2"></text>
        <text>停车场</text>
      </view>
      <view v-if="parkData.length > 0">
        <view
          class="park-box-one"
          v-for="(item, index) in parkData"
          :key="index"
          :style="getBoxStyle(parkData.length, index)"
        >
          <view class="top-title">
            <text>{{ item.parkingName }}</text>
            <view class="flex-1"></view>
            <text class="blance_car" @click="openPopup(item)">收费规则</text>
          </view>
          <!-- <view class="total">共{{ item.totalSpaceNum }}个车位</view> -->
          <view class="echars-box">
            <view class="echars-content">
              <l-echart :ref="(el) => (chartRefs[index] = el)"></l-echart>
            </view>

            <view class="echars-right">
              <view class="infos">
                <text class="info-top">总车位数</text>
                <text class="info-number" style="color:#68BBC4">{{ item.totalSpaceNum }}</text>
              </view>

              <view class="infos">
                <text class="info-top">空余车位</text>
                <text class="info-number">{{ item.remainingSpaceNum }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <noData v-if="parkData.length === 0"></noData>
    </view>

    <!-- 我的车辆 -->
    <view class="my-vehicles">
      <view class="vehicles-container">
        <!-- 标题区域 -->
        <view class="records-header">
          <view class="header-left">
            <text class="icon iconfonts icon-wodecheliang"></text>
            <text class="header-title">我的车辆</text>
            <view class="vehicle-count" v-if="carList.length > 0">
              <text>{{ carList.length }}</text>
            </view>
          </view>
        
        </view>

        

        <!-- 车辆列表 -->
        <view class="vehicles-list" v-if="carList.length > 0">
          <view
            class="vehicle-card"
            v-for="(car, carIndex) in carList"
            :key="carIndex"
            @click="viewVehicleDetail(car)"
          >
            <!-- 车辆主信息 -->
            <view class="vehicle-main">
              <view class="plate-section">
                <view class="plate-container">
                  <image :src="getPlateImage(car.plateNo)" class="plate-bg"></image>
                  <text class="plate-number" :style="{ color: getPlateColor(car.plateNo) }">
                    {{ car.plateNo }}
                  </text>
                </view>
                <view class="vehicle-type-badge" :class="getVehicleTypeClass(car.carType)">
                  {{ $formatDictLabel(car.carType, car_type) }}
                </view>
              </view>

              <!-- 车辆详情 -->
              <view class="vehicle-details">
                <view class="detail-item">
                  <view class="detail-icon">
                    <text class="iconfont icon-calendar">📅</text>
                  </view>
                  <view class="detail-content">
                    <text class="detail-label">有效期至</text>
                    <text class="detail-value" :class="getValidDateClass(car.validDate)">
                      {{ car.validDate }}
                    </text>
                  </view>
                </view>

                <view class="detail-item" v-if="car.brand">
                  <view class="detail-icon">
                    <text class="iconfonts icon-wodecheliang">🚗</text>
                  </view>
                  <view class="detail-content">
                    <text class="detail-label">车辆品牌</text>
                    <text class="detail-value">{{ car.brand || '未设置' }}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 操作区域 -->
            <view class="vehicle-actions">
              <view class="action-btn edit-btn" @click.stop="editCar(car)">
                <text class="iconfont icon-edit">✏️</text>
                <text class="action-text">编辑</text>
              </view>
              <view class="action-btn detail-btn" @click.stop="viewVehicleDetail(car)">
                <text class="iconfont icon-data-view">👁️</text>
                <text class="action-text">详情</text>
              </view>
            </view>

            <!-- 状态指示器 -->
            <view class="status-indicator" :class="getStatusClass(car.validDate)">
              <text class="status-dot"></text>
              <text class="status-text">{{ getStatusText(car.validDate) }}</text>
            </view>
          </view>
        </view>

        <noData v-if="carList.length === 0"></noData>
      </view>
    </view>

  

    <fee-rule-popup ref="popupRef" title="收费规则">
      <!-- 自定义弹窗内容 -->
      <view>
        <view class="one-box">
          <view class="title">
            <view class="left-line"></view>
            <view class="text">基础信息</view>
          </view>
          <view class="infos">
            <view class="one-info">
              <view class="one-info-left">所属车场：</view>
              <view class="one-info-right">{{ carInfo.parkingName }}</view>
            </view>
            <view class="one-info">
              <view class="one-info-left">规则名称：</view>
              <view class="one-info-right">{{ carInfo.ruleName }}</view>
            </view>
            <view class="one-info">
              <view class="one-info-left">适用车辆类型：</view>
              <view class="one-info-right">
                {{
                  carInfo.carTypeSplit
                    ? carInfo.carTypeSplit
                        .split(',')
                        .map((item) => $formatDictLabel(item.trim(), car_type))
                        .join('，')
                    : ''
                }}
              </view>
            </view>
            <view class="one-info">
              <view class="one-info-left">收费模式：</view>
              <view class="one-info-right">{{
                $formatDictLabel(
                  carInfo.chargeStatus,
                  park_charge_people_car_charge,
                )
              }}</view>
            </view>
            <view class="one-info">
              <view class="one-info-left">公休日收费：</view>
              <view
                class="one-info-right"
                v-if="carInfo.isHolidaysCharge == '1'"
                >否</view
              >
              <view
                class="one-info-right"
                v-if="carInfo.isHolidaysCharge == '0'"
                >是</view
              >
            </view>
          </view>
        </view>

        <view class="one-box">
          <view class="title">
            <view class="left-line"></view>
            <view class="text">免费时段</view>
          </view>
          <view class="infos">
            <view class="one-info">
              <view class="one-info-left">时间段类型：</view>
              <view class="one-info-right">
                {{
                  $formatDictLabel(carInfo.timePeriodType, time_period_type)
                }}</view
              >
            </view>
            <view class="one-info">
              <view class="one-info-left">连续停车标志：</view>
              <view class="one-info-right">是</view>
            </view>
            <view class="one-info">
              <view class="one-info-left">开始时间：</view>
              <view class="one-info-right">{{ carInfo.startTimePeriod }}</view>
            </view>
            <view class="one-info">
              <view class="one-info-left">次日结束时间：</view>
              <view class="one-info-right">{{ carInfo.endTimePeriod }}</view>
            </view>
          </view>
        </view>

        <view class="one-box">
          <view class="title">
            <view class="left-line"></view>
            <view class="text">加收条件</view>
          </view>
          <view class="infos">
            <view
              class="one-info"
              v-for="(item, index) in carInfo.rulesDays"
              :key="index"
            >
              <view class="one-info-left">{{ labelShow(item, index) }}：</view>
              <view class="one-info-right">{{ item.amount }}（元）</view>
            </view>
          </view>
        </view>
      </view>
    </fee-rule-popup>

    <!-- 近期停车记录 -->
    <view class="parking-records">
      <view class="parking-records-container">
        <!-- 标题区域 -->
        <view class="records-header">
          <view class="header-left">
            <text class="icon iconfonts icon-tingchejilu2"></text>
            <text class="header-title">近期停车记录</text>
          </view>
          <view class="header-right" @click="switchTab('parkingList')" v-if="parkInfo.plateNo">
            <text class="more-text">查看更多</text>
            <text class="iconfont icon-arrow-right more-icon"></text>
          </view>
        </view>

        <!-- 记录卡片 -->
        <view
          class="record-card"
          @click="switchTab('parkingList')"
          v-if="parkInfo.plateNo"
        >
          <!-- 车牌信息 -->
          <view class="plate-section">
            <view class="plate-info">
              <image :src="getPlateImage(parkInfo.plateNo)" class="plate-icon"></image>
              <text class="plate-number" :style="{ color: getPlateColor(parkInfo.plateNo) }">
                {{ parkInfo.plateNo }}
              </text>
            </view>
            <view class="car-type-badge">
              {{ $formatDictLabel(parkInfo.carType, car_type) }}
            </view>
          </view>

          <!-- 停车场信息 -->
          <view class="parking-info">
            <view class="info-item">
              <view class="info-icon">
                <text class="iconfonts icon-tingchechang"></text>
              </view>
              <view class="info-content">
                <text class="info-label">停车场</text>
                <text class="info-value">{{ parkInfo.parkingName }}</text>
              </view>
            </view>
          </view>

          <!-- 时间信息 -->
          <view class="time-section">
            <view class="time-item">
              <view class="time-dot enter-dot"></view>
              <view class="time-content">
                <text class="time-label">入场时间</text>
                <text class="time-value">{{ parkInfo.enterTime }}</text>
              </view>
            </view>
            <view class="time-line"></view>
            <view class="time-item">
              <view class="time-dot exit-dot"></view>
              <view class="time-content">
                <text class="time-label">出场时间</text>
                <text class="time-value">{{ parkInfo.outTime }}</text>
              </view>
            </view>
          </view>

          <!-- 停车时长 -->
          <view class="duration-section">
            <view class="duration-info">
              <text class="duration-label">停车时长</text>
              <text class="duration-value">{{ parkInfo.parkingDuration }} 小时</text>
            </view>
          </view>
        </view>

        <noData v-else></noData>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import * as echarts from 'echarts'
import { useUserStore } from '@/store/user.js'
import {
  findUserPark,
  findUserPlanetNo,
  findChargingRules,
  findRulesByIds,
  selectListByStaffId,
} from '@/common/api/park/record/index'
import { onPullDownRefresh, onShow } from '@dcloudio/uni-app'
const userStore = useUserStore()
const { proxy } = getCurrentInstance()
const { car_type, time_period_type, park_charge_people_car_charge } =
  proxy.useDict('car_type', 'time_period_type', 'park_charge_people_car_charge')

// 模拟动态数据
const parkData = ref([])

const chartRefs = ref([])
const color = ['#68BBC4', '#5087EC']
const activeTab = ref('home')

const switchTab = (tab) => {
  activeTab.value = tab
  // 这里可以添加切换逻辑，比如显示不同的内容
  switch (tab) {
    case 'home':
      uni.navigateTo({
        url: '/pages/vehicle-management/parking-management/index',
      })
      break
    case 'parkingList':
      uni.navigateTo({
        url: '/pages/vehicle-management/parking-record/index',
      })
      break
    case 'parkingOrder':
      uni.navigateTo({
        url: '/pages/vehicle-management/parking-order/index',
      })
      break
  }
}
const getList = async () => {
  try {
    uni.showLoading({
      title: '加载中',
    })
    setTimeout(function () {
      uni.hideLoading()
    }, 10000)
    const res = await findUserPark({})

    if (res.success) {
      parkData.value = res.data
      setTimeout(initCharts, 300)
    }
  } finally {
    uni.hideLoading()
  }
}

// 动态计算盒子样式
const getBoxStyle = (count, index) => {
  // if (count === 1) {
  //   return {
  //     gridColumn: '1 / -1',
  //     height: '400rpx',
  //     width: '100%',
  //     marginRight: '0px'
  //   }
  // } else {
  //   return {
  //     gridColumn: index === 2 ? '1 / -1' : '',
  //     height: '320rpx'
  //   }
  // }
  return {}
}

// 设置图表数据
const setChartOption = (data, index) => {
  const formatData = []

  // 已占用车位数据
  formatData.push(
    {
      value: data.totalSpaceNum - data.remainingSpaceNum,
      name: '已占用',
      label: {
        show: false,
        position: 'center',
      },

      itemStyle: {
        normal: {
          borderWidth: parkData.value.length == 1 ? 12 : 7,
          borderColor: color[0],
        },
      },
    },
    {
      value: 2,
      name: '',
      itemStyle: {
        normal: {
          label: { show: false },
          labelLine: { show: false },
          color: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 0,
        },
      },
    },
  )

  // 空余车位数据
  formatData.push(
    {
      value: data.remainingSpaceNum,
      label: {
        show: false,
        position: 'center',
      },
      name: '空余',
      itemStyle: {
        normal: {
          borderWidth: parkData.value.length == 1 ? 12 : 7,
          borderColor: color[1],
        },
      },
    },
    {
      value: 2,
      name: '',
      itemStyle: {
        normal: {
          label: { show: false },
          labelLine: { show: false },
          color: 'rgba(0, 0, 0, 0)',
          borderColor: 'rgba(0, 0, 0, 0)',
          borderWidth: 0,
        },
      },
    },
  )

  return {
    color: color,
    // 隐藏tooltip
    tooltip: {
      show: false,
      trigger: 'item',
      formatter: '{b}: {c}个 ({d}%)',
    },
    series: [
      {
        name: '',
        type: 'pie',
        radius: ['68%', '70%'],
        center: ['53%', '60%'],
        hoverAnimation: true,
        labelLine: { show: false },
        data: formatData,
      },
    ],
  }
}

// 初始化图表
const initCharts = async () => {
  parkData.value.forEach(async (item, index) => {
    if (chartRefs.value[index]) {
      const myChart = await chartRefs.value[index].init(echarts)
      myChart.setOption(setChartOption(item, index))
    }
  })
}

// 车辆数据
const carList = ref([])
const getList2 = async () => {
  try {
    setTimeout(function () {
      uni.hideLoading()
    }, 10000)
    const res = await findUserPlanetNo({})

    if (res.success) {
      carList.value = res.data
    }
  } finally {
    uni.hideLoading()
  }
}
// 根据车牌号判断是绿牌还是蓝牌
const getPlateImage = (plateNumber) => {
  // 新能源车牌通常是8位（绿牌）
  return plateNumber.length === 8 ? '/static/car-lv.png' : '/static/car-lan.png'
}

// 根据车牌号返回对应颜色
const getPlateColor = (plateNumber) => {
  return plateNumber.length === 8 ? '#28BA62' : '#5087EC'
}

// 获取车辆类型样式类
const getVehicleTypeClass = (carType) => {
  const typeMap = {
    '1': 'private-car',    // 私家车
    '2': 'official-car',   // 公务车
    '3': 'new-energy-car'  // 新能源车
  }
  return typeMap[carType] || 'default-car'
}

// 获取有效期样式类
const getValidDateClass = (validDate) => {
  if (!validDate) return 'date-unknown'

  const today = new Date()
  const valid = new Date(validDate)
  const diffTime = valid - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'date-expired'
  if (diffDays <= 30) return 'date-warning'
  return 'date-normal'
}

// 获取状态样式类
const getStatusClass = (validDate) => {
  if (!validDate) return 'status-unknown'

  const today = new Date()
  const valid = new Date(validDate)
  const diffTime = valid - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return 'status-expired'
  if (diffDays <= 30) return 'status-warning'
  return 'status-active'
}

// 获取状态文本
const getStatusText = (validDate) => {
  if (!validDate) return '未知'

  const today = new Date()
  const valid = new Date(validDate)
  const diffTime = valid - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays < 0) return '已过期'
  if (diffDays <= 30) return '即将过期'
  return '正常'
}

// 添加车辆
const addVehicle = () => {
  uni.navigateTo({
    url: '/pages/vehicle-management/parking-management/addCar'
  })
}

// 查看车辆详情
const viewVehicleDetail = (car) => {
  uni.navigateTo({
    url: '/pages/vehicle-management/parking-management/carDetail?item=' + JSON.stringify(car)
  })
}

const popupRef = ref()
const carInfo = ref({})
const openPopup = (item) => {
  popupRef.value.open()
  findChargingRulesFun(item)
}
const findRulesByIdsFun = async () => {
  try {
    uni.showLoading({
      title: '加载中',
    })

    const res = await findRulesByIds({
      id: carInfo.value.id, // 假设carInfo中有parkingId字段
    })

    if (res.success && res.data) {
      carInfo.value.rulesDays = res.data.rulesDays
    }
  } catch (error) {
    console.error('获取收费规则失败:', error)
    uni.showToast({
      title: '获取收费规则失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}
const findChargingRulesFun = async (item) => {
  try {
    uni.showLoading({
      title: '加载中',
    })
    setTimeout(function () {
      uni.hideLoading()
    }, 10000)
    const res = await findChargingRules({
      parkingName: item.parkingName,
    })

    if (res.success) {
      if (res.data.records && res.data.records.length > 0) {
        carInfo.value = res.data.records[0]
        findRulesByIdsFun()
      }
      uni.hideLoading()
    }
  } catch (error) {
    console.error('Error fetching data:', error)
  } finally {
    uni.hideLoading()
  }
}

const labelShow = (item, index) => {
  return carInfo.value.rulesDays.length - 1 == index
    ? '连续' + item.daySort + '天及以上'
    : '连续' + item.daySort + '天'
}

// 近期停车记录
const parkInfo = ref({})
const selectListByStaffIdFun = async () => {
  try {
    uni.showLoading({
      title: '加载中',
    })
    setTimeout(function () {
      uni.hideLoading()
    }, 10000)
    const res = await selectListByStaffId({
      pageNum: 1,
      pageSize: 10,
      staffId: userStore.userInfo.staffId,
    })

    if (res.success) {
      if (res.data.records && res.data.records.length > 0) {
        parkInfo.value = res.data.records[0]
      }
    }
  } finally {
    uni.hideLoading()
  }
}
// 添加下拉刷新处理
onPullDownRefresh(async () => {
  await Promise.all([getList(), getList2()])
  uni.stopPullDownRefresh()
})

// 修改车辆
const editCar = async (item) => {
  // 这里可以添加编辑车辆的逻辑
  uni.navigateTo({
    url:
      '/pages/vehicle-management/parking-management/editCar?item=' +
      JSON.stringify(item),
  })
}
// 添加 onShow 生命周期
onShow(() => {
  activeTab.value = 'home' // 默认选中第一个页签
  // 重新加载我的车辆
  getList2()
})
onMounted(() => {
  getList()
  selectListByStaffIdFun()
})
</script>

<style lang="scss" scoped>
.page-box {
  background: #f5f5f5;
  height: calc(100vh - 195rpx);
  overflow-y: auto;
}

.echars-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-left: 24rpx;
}

.park-box {
  padding: 20rpx;
  box-sizing: border-box;
  width: 100%;

  .park-box-one {
    width: 100%;
    height: 260rpx;
    background: #fff;
    border-radius: 8rpx;
    padding: 20rpx;
    box-sizing: border-box;
    margin-bottom: 20rpx;
    display: flex;
    flex-direction: column;

    .top-title {
      color: #000;
      font-size: 32rpx;
      margin-bottom: 12rpx;
      white-space: nowrap;
      /* 不换行 */
      overflow: hidden;
      /* 隐藏超出的内容 */
      text-overflow: ellipsis;
      /* 用省略号表示被隐藏的部分 */
      display: flex;
      align-items: center;

      .blance_car {
        background: #f2f2f2;
        color: #333333;
        padding: 5rpx 24rpx;
        font-size: 24rpx;
      }
    }

    .total {
      color: #000;
      font-size: 28rpx;
      margin-bottom: 12rpx;
    }

    .echars-box {
      display: flex;
      flex: 1;

      .echars-content {
        width: 150rpx;
        height: 150rpx;
      }

      .infos {
        .info-top {
          color: #666666;
          font-size: 28rpx;
          margin-right: 24rpx;
        }

        .info-number {
          color: #5087ec;
          font-size: 38rpx;
        }

        &.infos-big {
          .info-top {
            color: #666666;
            font-size: 32rpx;
            margin-bottom: 12rpx;
          }

          .info-number {
            color: #5087ec;
            font-size: 40rpx;
          }
        }
      }
    }

    &:nth-child(2n) {
      margin-right: 0px;
    }
  }
}

/* 我的车辆样式 */
.my-vehicles {
  padding: 20rpx;

  .vehicles-container {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  }



  .vehicles-list {
    padding: 20rpx;

    .vehicle-card {
      background: #fff;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      padding: 24rpx;
      border: 2rpx solid #f0f0f0;
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        transform: translateY(-2rpx);
        box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
        border-color: #c20000;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 6rpx;
        height: 100%;
        background: linear-gradient(to bottom, #c20000, #e74c3c);
      }

      .vehicle-main {
        margin-bottom: 20rpx;

        .plate-section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20rpx;

          .plate-container {
            display: flex;
            align-items: center;
            position: relative;

            .plate-bg {
              width: 60rpx;
              height: 60rpx;
              margin-right: 16rpx;
              border-radius: 8rpx;
              box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
            }

            .plate-number {
              font-size: 32rpx;
              font-weight: 600;
              letter-spacing: 2rpx;
              font-family: 'Courier New', monospace;
            }
          }

          .vehicle-type-badge {
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            font-weight: 500;
            color: #fff;

            &.private-car {
              background: linear-gradient(135deg, #667eea, #764ba2);
            }

            &.official-car {
              background: linear-gradient(135deg, #f093fb, #f5576c);
            }

            &.new-energy-car {
              background: linear-gradient(135deg, #4facfe, #00f2fe);
            }

            &.default-car {
              background: linear-gradient(135deg, #a8edea, #fed6e3);
              color: #333;
            }
          }
        }

        .vehicle-details {
          .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;
            padding: 12rpx 16rpx;
            background: #f8f9fa;
            border-radius: 12rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .detail-icon {
              width: 36rpx;
              height: 36rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #fff;
              border-radius: 50%;
              margin-right: 16rpx;
              box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

              .iconfont, .iconfonts {
                font-size: 20rpx;
                color: #c20000;
              }
            }

            .detail-content {
              flex: 1;

              .detail-label {
                display: block;
                font-size: 24rpx;
                color: #999;
                margin-bottom: 4rpx;
              }

              .detail-value {
                font-size: 28rpx;
                font-weight: 500;

                &.date-normal {
                  color: #4cd964;
                }

                &.date-warning {
                  color: #ff9500;
                }

                &.date-expired {
                  color: #ff3b30;
                }

                &.date-unknown {
                  color: #999;
                }
              }
            }
          }
        }
      }

      .vehicle-actions {
        display: flex;
        gap: 16rpx;
        margin-bottom: 16rpx;

        .action-btn {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 12rpx 20rpx;
          border-radius: 12rpx;
          font-size: 26rpx;
          transition: all 0.3s ease;

          .iconfont {
            font-size: 24rpx;
            margin-right: 8rpx;
          }

          &.edit-btn {
            background: #e3f2fd;
            color: #2196f3;
            border: 2rpx solid #bbdefb;

            &:active {
              background: #bbdefb;
            }
          }

          &.detail-btn {
            background: #f3e5f5;
            color: #9c27b0;
            border: 2rpx solid #e1bee7;

            &:active {
              background: #e1bee7;
            }
          }
        }
      }

      .status-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        .status-dot {
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
          margin-right: 8rpx;
        }

        &.status-active {
          background: rgba(76, 217, 100, 0.1);
          color: #4cd964;

          .status-dot {
            background: #4cd964;
          }
        }

        &.status-warning {
          background: rgba(255, 149, 0, 0.1);
          color: #ff9500;

          .status-dot {
            background: #ff9500;
          }
        }

        &.status-expired {
          background: rgba(255, 59, 48, 0.1);
          color: #ff3b30;

          .status-dot {
            background: #ff3b30;
          }
        }

        &.status-unknown {
          background: rgba(153, 153, 153, 0.1);
          color: #999;

          .status-dot {
            background: #999;
          }
        }
      }
    }
  }
}

.flex-1 {
  flex: 1;
}

.one-box {
  .title {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;

    .left-line {
      background: #c20000;
      width: 4rpx;
      height: 25rpx;
      margin-right: 12rpx;
    }

    .text {
      font-size: 28rpx;
      color: #c20000;
      position: relative;
      top: -2rpx;
    }
  }

  .infos {
    margin-bottom: 24rpx;

    .one-info {
      display: flex;
      align-items: center;
      min-height: 60rpx;
      border-bottom: 1px solid #eeee;
      padding: 10rpx 0px;

      .one-info-left {
        width: 220rpx;
        text-align: left;
        font-size: 28rpx;
        color: #666666;
        flex-shrink: 0;
      }

      .one-info-right {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        text-align: left;
      }
    }
  }
}



.emptys {
  text-align: center;
}

/* 近期停车记录样式 */
.parking-records {
  padding: 20rpx;
  padding-top: 0;

  .parking-records-container {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  }



  .record-card {
    padding: 24rpx;
    transition: all 0.3s ease;

    &:active {
      background: #f8f9fa;
    }

    .plate-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 24rpx;

      .plate-info {
        display: flex;
        align-items: center;

        .plate-icon {
          width: 48rpx;
          height: 48rpx;
          margin-right: 12rpx;
        }

        .plate-number {
          font-size: 32rpx;
          font-weight: 600;
          letter-spacing: 2rpx;
        }
      }

      .car-type-badge {
        background: linear-gradient(135deg, #c20000, #e74c3c);
        color: #fff;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;
        box-shadow: 0 2rpx 8rpx rgba(194, 0, 0, 0.3);
      }
    }

    .parking-info {
      margin-bottom: 24rpx;

      .info-item {
        display: flex;
        align-items: center;
        padding: 16rpx 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border-left: 6rpx solid #c20000;

        .info-icon {
          width: 40rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #fff;
          border-radius: 50%;
          margin-right: 16rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

          .iconfonts {
            font-size: 24rpx;
            color: #c20000;
          }
        }

        .info-content {
          flex: 1;

          .info-label {
            display: block;
            font-size: 24rpx;
            color: #999;
            margin-bottom: 4rpx;
          }

          .info-value {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
          }
        }
      }
    }

    .time-section {
      position: relative;
      margin-bottom: 24rpx;

      .time-item {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .time-dot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          position: relative;
          z-index: 2;

          &.enter-dot {
            background: #4cd964;
            box-shadow: 0 0 0 6rpx rgba(76, 217, 100, 0.2);
          }

          &.exit-dot {
            background: #ff9500;
            box-shadow: 0 0 0 6rpx rgba(255, 149, 0, 0.2);
          }
        }

        .time-content {
          flex: 1;
          padding: 16rpx 20rpx;
          background: #f8f9fa;
          border-radius: 12rpx;
          border-left: 4rpx solid transparent;

          .time-label {
            display: block;
            font-size: 24rpx;
            color: #999;
            margin-bottom: 6rpx;
          }

          .time-value {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            font-family: 'Courier New', monospace;
          }
        }
      }

      .time-line {
        position: absolute;
        left: 8rpx;
        top: 36rpx;
        bottom: 36rpx;
        width: 2rpx;
        background: linear-gradient(to bottom, #4cd964, #ff9500);
        z-index: 1;
      }
    }

    .duration-section {
      .duration-info {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20rpx;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 16rpx;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                      linear-gradient(-45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%),
                      linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%),
                      linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.1) 75%);
          background-size: 20rpx 20rpx;
          background-position: 0 0, 0 10rpx, 10rpx -10rpx, -10rpx 0rpx;
          opacity: 0.3;
        }

        .duration-label {
          font-size: 26rpx;
          color: rgba(255, 255, 255, 0.9);
          margin-right: 12rpx;
          position: relative;
          z-index: 1;
        }

        .duration-value {
          font-size: 32rpx;
          font-weight: 600;
          color: #fff;
          position: relative;
          z-index: 1;
        }
      }
    }
  }
}
.records-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 24rpx 20rpx;
    border-bottom: 2rpx solid #f5f5f5;

    .header-left {
      display: flex;
      align-items: center;

      .icon {
        margin-right: 12rpx;
        color: #c20000;
        font-size: 36rpx;
      }

      .header-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
      }

      .vehicle-count {
        background: #c20000;
        color: #fff;
        border-radius: 50%;
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: 600;
        box-shadow: 0 2rpx 8rpx rgba(194, 0, 0, 0.3);
        margin-left: 12rpx;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      background: #f8f9fa;
      transition: all 0.3s ease;

      &:active {
        background: #e9ecef;
        transform: scale(0.98);
      }

      .more-text {
        font-size: 26rpx;
        color: #666;
        margin-right: 8rpx;
      }

      .more-icon {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
.title {
  display: flex;
  align-items: center;
  .icon {
    margin-right: 12rpx;
    color: #333;
  }
  .icon-tingchejilu2 {
    font-size: 35rpx;
  }
  .icon-wodecheliang {
    font-size: 38rpx;
  }
  .icon-tingchechang2 {
    font-size: 34rpx;
  }
  .icon-tingchechang {
    font-size: 32rpx;
  }
  .icon-arrow-right {
    font-size: 24rpx;
  }
  .icon-add {
    font-size: 24rpx;
  }
  .icon-edit {
    font-size: 24rpx;
  }
  .icon-data-view {
    font-size: 24rpx;
  }
  .icon-calendar {
    font-size: 20rpx;
  }
  text {
    font-size: 32rpx;
    color: #333;
  }
}
</style>
