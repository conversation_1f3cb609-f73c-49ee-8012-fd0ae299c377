<template>
  <view class="image-container">
    <!-- 主图展示 -->
    <image 
    v-if="imageUrl"
      :src="imageUrl"
      :class="customClass"
      :mode="modeData"
      @error="handleError"
      @click="handlePreview"
      lazy-load
    >
      <!-- 错误状态插槽 -->
      <view v-if="!imageUrl" class="image-error">
        <u-icon name="photo-fail" size="40" color="#909399"></u-icon>
      </view>
    </image>

  </view>
</template>

<script setup>
import { ref, watch, onUnmounted, computed } from 'vue'
import { useTokenStore } from '@/store/token.js'
import { config } from '@/config/config.js'

const BASEURL =  config.BASEURL
const props = defineProps({
  photoId: {
    type: String,
    default: ''
  },
  customClass: {
    type: String,
    default: 'table-img'
  },
  initialIndex: {
    type: Number,
    default: 0
  },
  // 增加默认请求接口
  previewQueryUrl: {
    type: String,
    default:''
  },
  modeData: {
    type: String,
    default: 'aspectFill'
  },
  isShowCMSUrl: { // 是否显示CMS图片地址
    type: Boolean,
    default: false
  }
})
const IMG_URL = props.isShowCMSUrl?config.IMG_URL:config.IMG_URL2
const imageUrlVIew = computed(() => {
 return props.previewQueryUrl || `${BASEURL}${IMG_URL}`
})
const  tokenStore = useTokenStore()

const emit = defineEmits(['error'])

// 响应式数据
const imageUrl = ref('')
const isError = ref(false)
const blobUrls = ref(new Set())

// 清理 Blob URLs
const cleanupBlobUrls = () => {
  if (typeof window !== 'undefined') {
    blobUrls.value.forEach(url => URL.revokeObjectURL(url))
  }
  blobUrls.value.clear()
}

// 图片加载逻辑
const loadImage = async (photoId) => {
  isError.value = false
  cleanupBlobUrls()

  if (!photoId) {
    imageUrl.value = ''
    return
  }

  try {

    const response  = await uni.request({
      url: `${imageUrlVIew.value}${photoId}`,
      header: {
        'Authorization': `${tokenStore.tokenType}${tokenStore.value}`
      },
      responseType: 'arraybuffer'
    })
    const blobData = response.data;

    const blob = new Blob([blobData], { type: 'image/jpeg' })
    const blobUrl = URL.createObjectURL(blob)

  
    blobUrls.value.add(blobUrl)
    imageUrl.value = blobUrl
  } catch (error) {
    console.error("图片加载失败:", error)
    isError.value = true
    emit('error', error)
  }
}

// 预览处理
const handlePreview = () => {
  if (!imageUrl.value) return
  
  uni.previewImage({
    current: imageUrl.value,
    urls: [imageUrl.value],
    success: () => console.log('预览打开成功'),
    fail: (err) => console.error('预览失败:', err)
  })
}

// 错误处理
const handleError = (e) => {
  isError.value = true
  emit('error', e.detail.errMsg)
}

// 监听 photoId 变化
watch(() => props.photoId, (newVal) => {
  loadImage(newVal)
}, { immediate: true })

// 组件卸载清理
onUnmounted(() => {
  cleanupBlobUrls()
})
</script>

<style lang="scss" scoped>
.image-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.image-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
}

.table-img {
  border-radius: 8rpx;
  background-color: #f5f7fa;
  transition: opacity 0.3s;
  width: 100rpx;
  height: 100rpx;
  
  &:active {
    opacity: 0.8;
  }
}


</style>