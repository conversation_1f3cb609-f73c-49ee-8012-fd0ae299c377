import { request } from '@/common/net/request.js'

import { config } from '@/config/config.js'
let apiUrl = config.apiUrl
let apiHeader = config.apiHeader
let apiHeader2 = config.apiHeader2
// 停车记录
export function parkRecordsPage(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/majorParking/parkRecords/page`,
    method: 'post',
    params
  })

}

export function selectListByStaffId(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/majorParking/parkRecords/app/selectListByStaffId`,
    method: 'post',
    params
  })
}
export function gateControlByRecords(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/majorParking/parkRecords/gateControlByRecords`,
    method: 'post',
    params
  })
}

export function findRecordById(id) {
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/majorParking/parkRecords/findRecordById/${id}`,
    method: 'post',
  })
}


export function findParkOrdersPage(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/majorParking/parkOrders/findParkOrdersPage`,
    method: "post",
    params
  })
}
export function findParkOrdersById(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/majorParking/parkOrders/findParkOrdersById`,
    method: 'post',
    params
  })
}



export function findUserPark(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/app/majorParking/parkingLot/findUserPark`,
    method: 'post',
    params
  })
}

export function findUserPlanetNo(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/app/majorParking/parkingLot/findUserPlanetNo`,
    method: 'post',
    params
  })
}



export function findRulesById(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/majorParking/parkChargingRules/findRulesById`,
    method: 'post',
    params
  })
}


export function findChargingRules(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/app/majorParking/parkingLot/findChargingRules`,
    method: 'post',
    params
  })
}



export function findRulesByIds(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/app/majorParking/parkingLot/findRulesById`,
    method: 'post',
    params
  })
}

export function updataUserCar(params) {
  params['uniContentType'] = 'json'
  return request({
    url: `${apiHeader}/park${apiUrl}${apiHeader2}/majorParking/userCar/app/updateUserCar`,
    method: "post",
    params
  })
}



