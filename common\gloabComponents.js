// src/components/global/index.js
import PreviewImage from '@/components/PreviewImage/index.vue'
import noData from '@/components/no-data/index.vue'
import dingtalkNavBar from '@/components/dingtalk-nav-bar/index.vue'
import LoadMore from '@/components/LoadMore/index.vue'
import FeeRulePopup from '@/components/FeeRulePopup/index.vue'
import imageUpload from '@/components/image-upload/index.vue'
const GlobalComponents = {
  install: (app) => {
    // 注册全局图片预览组件
    app.component('PreviewImage', PreviewImage)
    app.component('noData', noData)
    app.component('dingtalkNavBar', dingtalkNavBar)
    app.component('LoadMore', LoadMore)
    app.component('FeeRulePopup', FeeRulePopup)
     app.component('imageUpload', imageUpload)
    // 可在此继续注册其他全局组件
  }
}

export default GlobalComponents