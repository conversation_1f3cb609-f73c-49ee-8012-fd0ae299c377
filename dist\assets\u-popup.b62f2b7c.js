import{_ as o}from"./u-transition.ad7ea666.js";import{d as e,m as t,a as s,r as a,b as i,o as l,c as r,w as n,e as u,g as p,f as d,n as c,M as m,s as y,k as f}from"./index-0e73d719.js";import{_ as h}from"./_plugin-vue_export-helper.1b428a4d.js";import{_ as b}from"./u-status-bar.02813dc9.js";import{_ as v}from"./u-icon.0412d68c.js";import{_ as g}from"./u-safe-bottom.28c88172.js";const _=h({name:"u-overlay",mixins:[t,s,{props:{show:{type:Boolean,default:e.overlay.show},zIndex:{type:[String,Number],default:e.overlay.zIndex},duration:{type:[String,Number],default:e.overlay.duration},opacity:{type:[String,Number],default:e.overlay.opacity}}}],computed:{overlayStyle(){const o={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return uni.$u.deepMerge(o,uni.$u.addStyle(this.customStyle))}},emits:["click"],methods:{clickHandler(){this.$emit("click")}}},[["render",function(e,t,s,p,d,c){const m=a(i("u-transition"),o);return l(),r(m,{show:e.show,"custom-class":"u-overlay",duration:e.duration,"custom-style":c.overlayStyle,onClick:c.clickHandler},{default:n((()=>[u(e.$slots,"default",{},void 0,!0)])),_:3},8,["show","duration","custom-style","onClick"])}],["__scopeId","data-v-82a76b03"]]);const k=h({name:"u-popup",mixins:[t,s,{props:{show:{type:Boolean,default:e.popup.show},overlay:{type:Boolean,default:e.popup.overlay},mode:{type:String,default:e.popup.mode},duration:{type:[String,Number],default:e.popup.duration},closeable:{type:Boolean,default:e.popup.closeable},overlayStyle:{type:[Object,String],default:e.popup.overlayStyle},closeOnClickOverlay:{type:Boolean,default:e.popup.closeOnClickOverlay},zIndex:{type:[String,Number],default:e.popup.zIndex},safeAreaInsetBottom:{type:Boolean,default:e.popup.safeAreaInsetBottom},safeAreaInsetTop:{type:Boolean,default:e.popup.safeAreaInsetTop},closeIconPos:{type:String,default:e.popup.closeIconPos},round:{type:[Boolean,String,Number],default:e.popup.round},zoom:{type:Boolean,default:e.popup.zoom},bgColor:{type:String,default:e.popup.bgColor},overlayOpacity:{type:[Number,String],default:e.popup.overlayOpacity}}}],data(){return{overlayDuration:this.duration+50}},watch:{show(o,e){}},computed:{transitionStyle(){const o={zIndex:this.zIndex,position:"fixed",display:"flex"};return o[this.mode]=0,"left"===this.mode||"right"===this.mode?uni.$u.deepMerge(o,{bottom:0,top:0}):"top"===this.mode||"bottom"===this.mode?uni.$u.deepMerge(o,{left:0,right:0}):"center"===this.mode?uni.$u.deepMerge(o,{alignItems:"center","justify-content":"center",top:0,left:0,right:0,bottom:0}):void 0},contentStyle(){const o={};if(uni.$u.sys(),"center"!==this.mode&&(o.flex=1),this.bgColor&&(o.backgroundColor=this.bgColor),this.round){const e=uni.$u.addUnit(this.round);"top"===this.mode?(o.borderBottomLeftRadius=e,o.borderBottomRightRadius=e):"bottom"===this.mode?(o.borderTopLeftRadius=e,o.borderTopRightRadius=e):"center"===this.mode&&(o.borderRadius=e)}return uni.$u.deepMerge(o,uni.$u.addStyle(this.customStyle))},position(){return"center"===this.mode?this.zoom?"fade-zoom":"fade":"left"===this.mode?"slide-left":"right"===this.mode?"slide-right":"bottom"===this.mode?"slide-up":"top"===this.mode?"slide-down":void 0}},emits:["open","close","click"],methods:{overlayClick(){this.closeOnClickOverlay&&this.$emit("close")},close(o){this.$emit("close")},afterEnter(){this.$emit("open")},clickHandler(){"center"===this.mode&&this.overlayClick(),this.$emit("click")}}},[["render",function(e,t,s,h,k,S){const C=a(i("u-overlay"),_),I=a(i("u-status-bar"),b),w=a(i("u-icon"),v),x=f,$=a(i("u-safe-bottom"),g),B=a(i("u-transition"),o);return l(),r(x,{class:"u-popup"},{default:n((()=>[e.overlay?(l(),r(C,{key:0,show:e.show,onClick:S.overlayClick,duration:k.overlayDuration,customStyle:e.overlayStyle,opacity:e.overlayOpacity},null,8,["show","onClick","duration","customStyle","opacity"])):p("",!0),d(B,{show:e.show,customStyle:S.transitionStyle,mode:S.position,duration:e.duration,onAfterEnter:S.afterEnter,onClick:S.clickHandler},{default:n((()=>[d(x,{class:"u-popup__content",style:c([S.contentStyle]),onClick:m(e.noop,["stop"])},{default:n((()=>[e.safeAreaInsetTop?(l(),r(I,{key:0})):p("",!0),u(e.$slots,"default",{},void 0,!0),e.closeable?(l(),r(x,{key:1,onClick:m(S.close,["stop"]),class:y(["u-popup__content__close",["u-popup__content__close--"+e.closeIconPos]]),"hover-class":"u-popup__content__close--hover","hover-stay-time":"150"},{default:n((()=>[d(w,{name:"close",color:"#909399",size:"18",bold:""})])),_:1},8,["onClick","class"])):p("",!0),e.safeAreaInsetBottom?(l(),r($,{key:2})):p("",!0)])),_:3},8,["style","onClick"])])),_:3},8,["show","customStyle","mode","duration","onAfterEnter","onClick"])])),_:3})}],["__scopeId","data-v-27ba5882"]]);export{k as _};
