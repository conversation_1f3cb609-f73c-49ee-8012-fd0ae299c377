<template>
  <view class="page">
    <view class="header">
      <text class="title">字体图标测试</text>
    </view>
    
    <view class="content">
      <!-- 方法1: 直接使用Unicode -->
      <view class="test-group">
        <text class="group-title">方法1: 直接Unicode</text>
        <view class="icon-row">
          <text class="icon-item unicode-icon">&#xe665;</text>
          <text class="icon-item unicode-icon">&#xe667;</text>
          <text class="icon-item unicode-icon">&#xe615;</text>
        </view>
      </view>
      
      <!-- 方法2: 使用CSS类 -->
      <view class="test-group">
        <text class="group-title">方法2: CSS类</text>
        <view class="icon-row">
          <text class="icon-item iconfont icon-arrow-right"></text>
          <text class="icon-item iconfont icon-calendar"></text>
          <text class="icon-item iconfonts icon-wodecheliang"></text>
        </view>
      </view>
      
      <!-- 方法3: 内联样式 -->
      <view class="test-group">
        <text class="group-title">方法3: 内联样式</text>
        <view class="icon-row">
          <text class="icon-item" style="font-family: 'iconfont';">&#xe665;</text>
          <text class="icon-item" style="font-family: 'iconfont';">&#xe667;</text>
          <text class="icon-item" style="font-family: 'iconfonts';">&#xe615;</text>
        </view>
      </view>
      
      <!-- 字体加载状态检查 -->
      <view class="test-group">
        <text class="group-title">字体加载检查</text>
        <view class="font-check">
          <text class="check-item">iconfont字体: {{ iconfontLoaded ? '✅ 已加载' : '❌ 未加载' }}</text>
          <text class="check-item">iconfonts字体: {{ iconfontsLoaded ? '✅ 已加载' : '❌ 未加载' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const iconfontLoaded = ref(false)
const iconfontsLoaded = ref(false)

onMounted(() => {
  console.log('简单图标测试页面加载')
  
  // 检查字体加载状态
  setTimeout(() => {
    checkFontLoading()
  }, 1000)
})

function checkFontLoading() {
  // #ifdef H5
  if (typeof document !== 'undefined') {
    try {
      // 检查iconfont
      const testElement1 = document.createElement('span')
      testElement1.style.fontFamily = 'iconfont'
      testElement1.style.fontSize = '16px'
      testElement1.innerHTML = '&#xe665;'
      document.body.appendChild(testElement1)
      
      const computedStyle1 = window.getComputedStyle(testElement1)
      iconfontLoaded.value = computedStyle1.fontFamily.includes('iconfont')
      
      // 检查iconfonts
      const testElement2 = document.createElement('span')
      testElement2.style.fontFamily = 'iconfonts'
      testElement2.style.fontSize = '16px'
      testElement2.innerHTML = '&#xe615;'
      document.body.appendChild(testElement2)
      
      const computedStyle2 = window.getComputedStyle(testElement2)
      iconfontsLoaded.value = computedStyle2.fontFamily.includes('iconfonts')
      
      // 清理测试元素
      document.body.removeChild(testElement1)
      document.body.removeChild(testElement2)
      
      console.log('iconfont加载状态:', iconfontLoaded.value)
      console.log('iconfonts加载状态:', iconfontsLoaded.value)
    } catch (error) {
      console.error('字体检查失败:', error)
    }
  }
  // #endif
  
  // #ifndef H5
  // 在非H5环境下，假设字体已加载
  iconfontLoaded.value = true
  iconfontsLoaded.value = true
  // #endif
}
</script>

<style lang="scss">
/* 直接在页面中定义字体 */
@font-face {
  font-family: 'iconfont';
  font-weight: normal;
  font-style: normal;
  src: url('/static/iconfont/iconfont.ttf') format('truetype');
}

@font-face {
  font-family: 'iconfonts';
  font-weight: normal;
  font-style: normal;
  src: url('/static/iconfonts/iconfont.ttf') format('truetype');
}

.page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
}

.test-group {
  margin-bottom: 40rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.group-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.icon-row {
  display: flex;
  gap: 30rpx;
  align-items: center;
}

.icon-item {
  font-size: 48rpx;
  color: #c20000;
  line-height: 1;
  display: inline-block;
  padding: 10rpx;
  border: 2rpx solid #eee;
  border-radius: 8rpx;
  background: #fafafa;
  text-align: center;
  min-width: 80rpx;
}

.unicode-icon {
  font-family: 'iconfont', 'iconfonts', sans-serif;
}

.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal !important;
  font-weight: normal !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-style: normal !important;
  font-weight: normal !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.font-check {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.check-item {
  font-size: 28rpx;
  color: #666;
  padding: 15rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
}

/* 图标定义 */
.icon-arrow-right:before { content: '\e665'; }
.icon-calendar:before { content: '\e667'; }
.icon-wodecheliang:before { content: "\e615"; }
</style>
