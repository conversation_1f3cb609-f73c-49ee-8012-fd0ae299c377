<template>
  <view class="preview-page">
    <view class="preview-header">
      <text class="preview-title">我的车辆样式预览</text>
    </view>

    <!-- 我的车辆组件预览 -->
    <view class="my-vehicles">
      <view class="vehicles-container">
        <!-- 标题区域 -->
        <view class="vehicles-header">
          <view class="header-left">
            <text class="icon iconfonts icon-wodecheliang"></text>
            <text class="header-title">我的车辆</text>
            <view class="vehicle-count">
              <text>4</text>
            </view>
          </view>
          <view class="header-right">
            <text class="add-text">添加车辆</text>
            <text class="iconfont icon-add add-icon">+</text>
          </view>
        </view>

        <!-- 车辆列表 -->
        <view class="vehicles-list">
          <!-- 车辆卡片1 - 正常状态 -->
          <view class="vehicle-card">
            <view class="vehicle-main">
              <view class="plate-section">
                <view class="plate-container">
                  <image src="/static/car-lan.png" class="plate-bg"></image>
                  <text class="plate-number" style="color: #5087EC;">京A12345</text>
                </view>
                <view class="vehicle-type-badge private-car">私家车</view>
              </view>

              <view class="vehicle-details">
                <view class="detail-item">
                  <view class="detail-icon">
                    <text class="iconfont icon-calendar">📅</text>
                  </view>
                  <view class="detail-content">
                    <text class="detail-label">有效期至</text>
                    <text class="detail-value date-normal">2024-12-31</text>
                  </view>
                </view>

                <view class="detail-item">
                  <view class="detail-icon">
                    <text class="iconfonts icon-wodecheliang">🚗</text>
                  </view>
                  <view class="detail-content">
                    <text class="detail-label">车辆品牌</text>
                    <text class="detail-value">奔驰 E300L</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="vehicle-actions">
              <view class="action-btn edit-btn">
                <text class="iconfont">✏️</text>
                <text class="action-text">编辑</text>
              </view>
              <view class="action-btn detail-btn">
                <text class="iconfont">👁️</text>
                <text class="action-text">详情</text>
              </view>
            </view>

            <view class="status-indicator status-active">
              <text class="status-dot"></text>
              <text class="status-text">正常</text>
            </view>
          </view>

          <!-- 车辆卡片2 - 新能源车 -->
          <view class="vehicle-card">
            <view class="vehicle-main">
              <view class="plate-section">
                <view class="plate-container">
                  <image src="/static/car-lv.png" class="plate-bg"></image>
                  <text class="plate-number" style="color: #28BA62;">粤C新能源001</text>
                </view>
                <view class="vehicle-type-badge new-energy-car">新能源车</view>
              </view>

              <view class="vehicle-details">
                <view class="detail-item">
                  <view class="detail-icon">
                    <text class="iconfont icon-calendar">📅</text>
                  </view>
                  <view class="detail-content">
                    <text class="detail-label">有效期至</text>
                    <text class="detail-value date-normal">2025-03-15</text>
                  </view>
                </view>

                <view class="detail-item">
                  <view class="detail-icon">
                    <text class="iconfonts icon-wodecheliang">🚗</text>
                  </view>
                  <view class="detail-content">
                    <text class="detail-label">车辆品牌</text>
                    <text class="detail-value">特斯拉 Model Y</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="vehicle-actions">
              <view class="action-btn edit-btn">
                <text class="iconfont">✏️</text>
                <text class="action-text">编辑</text>
              </view>
              <view class="action-btn detail-btn">
                <text class="iconfont">👁️</text>
                <text class="action-text">详情</text>
              </view>
            </view>

            <view class="status-indicator status-active">
              <text class="status-dot"></text>
              <text class="status-text">正常</text>
            </view>
          </view>

          <!-- 车辆卡片3 - 即将过期 -->
          <view class="vehicle-card">
            <view class="vehicle-main">
              <view class="plate-section">
                <view class="plate-container">
                  <image src="/static/car-lan.png" class="plate-bg"></image>
                  <text class="plate-number" style="color: #5087EC;">浙D66666</text>
                </view>
                <view class="vehicle-type-badge official-car">公务车</view>
              </view>

              <view class="vehicle-details">
                <view class="detail-item">
                  <view class="detail-icon">
                    <text class="iconfont icon-calendar">📅</text>
                  </view>
                  <view class="detail-content">
                    <text class="detail-label">有效期至</text>
                    <text class="detail-value date-expired">2024-01-15</text>
                  </view>
                </view>

                <view class="detail-item">
                  <view class="detail-icon">
                    <text class="iconfonts icon-wodecheliang">🚗</text>
                  </view>
                  <view class="detail-content">
                    <text class="detail-label">车辆品牌</text>
                    <text class="detail-value">奥迪 A6L</text>
                  </view>
                </view>
              </view>
            </view>

            <view class="vehicle-actions">
              <view class="action-btn edit-btn">
                <text class="iconfont">✏️</text>
                <text class="action-text">编辑</text>
              </view>
              <view class="action-btn detail-btn">
                <text class="iconfont">👁️</text>
                <text class="action-text">详情</text>
              </view>
            </view>

            <view class="status-indicator status-expired">
              <text class="status-dot"></text>
              <text class="status-text">已过期</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  console.log('车辆预览页面加载完成')
})
</script>

<style lang="scss">
@import '~@/common/styles/iconfont.scss';

.preview-page {
  background: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.preview-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.preview-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 复制主页面的车辆样式 */
.my-vehicles {
  .vehicles-container {
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  }

  .vehicles-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 24rpx 20rpx;
    border-bottom: 2rpx solid #f5f5f5;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);

    .header-left {
      display: flex;
      align-items: center;

      .icon {
        margin-right: 12rpx;
        color: #c20000;
        font-size: 38rpx;
      }

      .header-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-right: 12rpx;
      }

      .vehicle-count {
        background: #c20000;
        color: #fff;
        border-radius: 50%;
        width: 40rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        font-weight: 600;
        box-shadow: 0 2rpx 8rpx rgba(194, 0, 0, 0.3);
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      background: #c20000;
      color: #fff;

      .add-text {
        font-size: 26rpx;
        margin-right: 8rpx;
      }

      .add-icon {
        font-size: 24rpx;
      }
    }
  }

  .vehicles-list {
    padding: 20rpx;

    .vehicle-card {
      background: #fff;
      border-radius: 16rpx;
      margin-bottom: 20rpx;
      padding: 24rpx;
      border: 2rpx solid #f0f0f0;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 6rpx;
        height: 100%;
        background: linear-gradient(to bottom, #c20000, #e74c3c);
      }

      .vehicle-main {
        margin-bottom: 20rpx;

        .plate-section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20rpx;

          .plate-container {
            display: flex;
            align-items: center;

            .plate-bg {
              width: 60rpx;
              height: 60rpx;
              margin-right: 16rpx;
              border-radius: 8rpx;
              box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
            }

            .plate-number {
              font-size: 32rpx;
              font-weight: 600;
              letter-spacing: 2rpx;
              font-family: 'Courier New', monospace;
            }
          }

          .vehicle-type-badge {
            padding: 8rpx 16rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            font-weight: 500;
            color: #fff;

            &.private-car {
              background: linear-gradient(135deg, #667eea, #764ba2);
            }

            &.official-car {
              background: linear-gradient(135deg, #f093fb, #f5576c);
            }

            &.new-energy-car {
              background: linear-gradient(135deg, #4facfe, #00f2fe);
            }
          }
        }

        .vehicle-details {
          .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;
            padding: 12rpx 16rpx;
            background: #f8f9fa;
            border-radius: 12rpx;

            .detail-icon {
              width: 36rpx;
              height: 36rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              background: #fff;
              border-radius: 50%;
              margin-right: 16rpx;
              box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
            }

            .detail-content {
              flex: 1;

              .detail-label {
                display: block;
                font-size: 24rpx;
                color: #999;
                margin-bottom: 4rpx;
              }

              .detail-value {
                font-size: 28rpx;
                font-weight: 500;

                &.date-normal {
                  color: #4cd964;
                }

                &.date-expired {
                  color: #ff3b30;
                }
              }
            }
          }
        }
      }

      .vehicle-actions {
        display: flex;
        gap: 16rpx;
        margin-bottom: 16rpx;

        .action-btn {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 12rpx 20rpx;
          border-radius: 12rpx;
          font-size: 26rpx;

          .iconfont {
            margin-right: 8rpx;
          }

          &.edit-btn {
            background: #e3f2fd;
            color: #2196f3;
            border: 2rpx solid #bbdefb;
          }

          &.detail-btn {
            background: #f3e5f5;
            color: #9c27b0;
            border: 2rpx solid #e1bee7;
          }
        }
      }

      .status-indicator {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;

        .status-dot {
          width: 12rpx;
          height: 12rpx;
          border-radius: 50%;
          margin-right: 8rpx;
        }

        &.status-active {
          background: rgba(76, 217, 100, 0.1);
          color: #4cd964;

          .status-dot {
            background: #4cd964;
          }
        }

        &.status-expired {
          background: rgba(255, 59, 48, 0.1);
          color: #ff3b30;

          .status-dot {
            background: #ff3b30;
          }
        }
      }
    }
  }
}
</style>
