import { request } from '../request.js'
import { joinUrlParams } from '@/common/uniUtils.js'
import {
  ACCREDIT_CODE_URL,
  ACCREDIT_TOKEN_URL,
  ACCREDIT_SEND_URL,
  ACCREDIT_BINDING_URL,
} from '@/common/net/netUrl.js'

// 根据code获取access_token
export function accredit(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: ACCREDIT_CODE_URL,
    method: 'GET',
    params,
  })
}
// 根据access_token换取真正的token
export function wechatlogin(pathParams) {
  let localUrl = joinUrlParams(ACCREDIT_TOKEN_URL, pathParams)
  return request({
    url: localUrl,
    method: 'POST',
  })
}
// 发送验证码
export function sendVerification(params) {
  console.log(params)
  params['uniContentType'] = 'json'
  return request({
    url: ACCREDIT_SEND_URL,
    method: 'POST',
    params,
  })
}
// 绑定手机号
export function binding(params) {
  params['uniContentType'] = 'json'
  console.log(params)
  return request({
    url: ACCREDIT_BINDING_URL,
    method: 'POST',
    params,
  })
}
