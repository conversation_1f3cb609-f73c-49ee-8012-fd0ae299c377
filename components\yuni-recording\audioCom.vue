<template>
  <view class="audio-component">
    <view class="audio-slider">
      <!-- <view class="img-box" @click="playAudio">
               <u-icon :name="state ?'pause':'play-right-fill'" size="30" color ='#0c91e4'>
               </u-icon>

            </view> -->
      <image
        class="playbtn"
        @click.stop.prevent="playAudio"
        :src="
          state
            ? '@/static/images/sound-recording/pause.png'
            : '@/static/images/sound-recording/play.png'
        "
      ></image>
      <slider
        :block-size="10"
        :value="sliderProgressValue"
        :min="0"
        :max="sliderMax"
        activeColor="#0BBD88"
        block-color="#0BBD88"
        backgroundColor="#CBCBCB"
        step="1"
		disabled
      />

      <!-- <view class="time-Show">{{ currentTimeStr }}</view> -->
    </view>
    <view class="close-box" @click="close" v-if="closeable">
      <image :src="OVAL_URL"></image>
    </view>
  </view>
</template>

<script setup>
import { nextTick, ref, watchEffect, onBeforeUnmount, reactive } from 'vue'
import { onUnload,onLoad } from '@dcloudio/uni-app'
import { OVAL_URL } from '@/common/net/staticUrl'
const emit = defineEmits(['AudioDestroy']) 

let mp3player = {}

const sliderMax = ref(1)
const sliderProgressValue = ref(0)
const sliderValue = ref('00:00')
const currentTimeStr = ref('00:00')
const state = ref(false)

const props = defineProps({
  presentData: {
    type: Object,
    default: () => {},
  },
  closeable: {
    type: Boolean,
    default: false,
  },
})
const audioSrc = ref(props.presentData.src)

onLoad(() => {
	uni.downloadFile({
		url: props.presentData.src, //仅为示例，并非真实的资源
		success: (res) => {
			if (res.statusCode === 200) {
				audioSrc.value = res.tempFilePath
				// 获取音频长度
				getAudioLength(res.tempFilePath)
			}
		}
	});
})

const _audio = uni.createInnerAudioContext()
function getAudioLength(src){
	_audio.src = src
	_audio.onCanplay(() => {
		sliderMax.value = _audio.duration
	})
}
onUnload(() => {
	_audio.destroy()
})


watchEffect(()=>{
	// console.log('watchEffect');
	sliderProgressValue.value = 0
})

function playAudio() {
  state.value = !state.value
  // false暂停  true播放
  if (state.value) {
    // mp3player.autoplay = true
	console.log('mp3player',mp3player);
	if(Reflect.ownKeys(mp3player).length === 0){
		mp3player = uni.createInnerAudioContext({ useWebAudioImplement: true })
		mp3player.volume = 1
		mp3player.startTime = 0
		   
		sliderProgressValue.value = 0
		currentTimeStr.value = '00:00'
		mp3player.src = props.presentData.src
		creatAudio()
		mp3player.play()
	}else {
		mp3player.play()
	}
	 //播放 
  } else {
	console.log("暂停");
	mp3player.pause() //暂停
	mp3player.onPause(() => {
		mp3player.startTime = mp3player.currentTime
	})
  }
}
//实时播放时长
function creatAudio() {
	mp3player.onCanplay((res) => {
		let { currentTime, duration } = mp3player
		let currTimeStr = formatTime(duration)
		// 未转化 时间格式的实时时长
		// 实时变动的时间
		currentTimeStr.value = currTimeStr
	})

  // 播放中的实时监听 播放位置 以及时长
  mp3player.onTimeUpdate((res) => {
    const { currentTime, duration } = mp3player //这俩参数是这个api自带的参数, 解构
    let currTimeStr = formatTime(currentTime)
    // 未转化 时间格式的实时时长
    sliderProgressValue.value = Math.floor(currentTime)
    // 实时变动的时间
    currentTimeStr.value = currTimeStr
  })
  
	mp3player.onPlay((res) => {
		console.log('onplay',res);
		const duration = mp3player.duration //这俩参数是这个api自带的参数, 解构

		// sliderMax.value = Math.floor(5)
		// console.log('sliderMax',sliderMax.value);
		 //音频总时长
		sliderValue.value = formatSecond(sliderMax.value)
		console.log('总时长', sliderValue.value)
	})
	
	// 错误事件监听
	mp3player.onError((res) => {
		// uni.showToast({
		// 	title:"播放出错",
		// 	icon: 'none'
		// })
		// state.value = false
		console.log('错误',res.errMsg)
		console.log(res.errCode)
	})
  // 监听播放结束 的处理
	mp3player.onEnded(() => {
		console.log('播放结束')
		sliderProgressValue.value = sliderMax.value
		setTimeout(() => {
			currentTimeStr.value = sliderValue.value
			sliderProgressValue.value = 0
			state.value = false
			mp3player.destroy()
			mp3player = {}
		}, 1000)
	})
}

//格式化时间格式
function formatTime(num) {
  num = Math.floor(num)
  let second = num % 60
  if (second < 10) second = '0' + second
  let min = Math.floor(num / 60)
  if (min < 10) min = '0' + min
  return min + ':' + second
}
// 拖拽音频播放位置
function sliderChange(e) {
  // console.log('value 发生变化：' + e.detail.value,this.state,this.innerAudioContext)
  const currTimeStr = formatTime(e.detail.value)
  currentTimeStr.value = currTimeStr
  // 播放进度条位置
  sliderProgressValue.value = e.detail.value
  //设置要播放的位置
  mp3player.seek(e.detail.value)
  // this.innerAudioContext.pause()//暂停
  // // 因为拖拽后自动播放 导致实时获取播放时间的方法没有执行【坑点】
  // setTimeout(()=>{
  //  //模拟点击播放
  //  this.state=false
  //  this.playAudio()
  // },1000)
}
function formatSecond(seconds) {
  var h =
    Math.floor(seconds / 3600) < 10
      ? '0' + Math.floor(seconds / 3600)
      : Math.floor(seconds / 3600)
  var m =
    Math.floor((seconds / 60) % 60) < 10
      ? '0' + Math.floor((seconds / 60) % 60)
      : Math.floor((seconds / 60) % 60)
  var s =
    Math.floor(seconds % 60) < 10
      ? '0' + Math.floor(seconds % 60)
      : Math.floor(seconds % 60)
  // return  h + ":" + m + ":" + s;
  return m + ':' + s
}
// 关闭
// 关闭
function close() {
	console.log('close',mp3player)
  sliderMax.value = '' //  例如30     音频总时长
  sliderProgressValue.value = 0 //  例如11     实时进度
  sliderValue.value = '00:00' //  例如00:30 最大值
  currentTimeStr.value = '00:00' //  例如00:11 音频实时播放进度
  if (state.value) {
	  console.log('close',mp3player)
  	  mp3player.destroy()
  }
  state.value = false
  mp3player = {} 
  emit('AudioDestroy', '关闭')
}
// onBeforeUnmount(() => {
//   if (mp3player) {
//       mp3player.destroy()
//     }
// })
</script>

<style lang="scss" scoped>
.audio-component {
  background: #ffffff;
  box-shadow: 1rpx 2rpx 8rpx 6rpx rgba(79, 139, 250, 0.05);
  border-radius: 10rpx;
  position: relative;
  padding: 20rpx 23rpx;
  margin-top: 20rpx;
  // position: fixed;
  // width: calc(100% - 60rpx);
  // left: 0;
  // bottom: 120rpx;
  // margin: 0 30rpx;
}
.audio-slider {
  display: flex;
  align-items: center;
  slider {
    flex: 1;
    margin: 6rpx 0 6rpx 20rpx;
  }
  .playbtn {
    width: 40rpx;
    height: 40rpx;
  }
}
.time-Show {
  padding-left: 20rpx;
  font-size: 22rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #0bbd88;
  line-height: 30rpx;
}
.close-box {
  position: absolute;
  right: -18rpx;
  top: -18rpx;
  image {
    width: 35rpx;
    height: 35rpx;
  }
}
</style>
