export const API_LOGIN_URL = '/auth/login' //登录
export const API_LOGOUT_URL = '/auth/logout' //登出
export const API_USERINFO_URL = '/auth/oauth/check_token' //获取用户信息
export const API_TRAREN_URL = '/user/tenants/info' //获取租户信息
export const API_REFRESH_URL = '/auth/oauth/token' //刷新token
export const API_SWITCHJOB_URL = '/auth/jobSelect' //切换岗位
export const API_VERFICATION_URL = '/user/staffs/getVerificationCode' //获取短信验证码
export const API_FORGOT_URL = '/user/staffs/forgottenPassword' //找回密码
export const API_SYSTEM_URL = '/cms/v1.0/newVersion/forcheck' //系统检查更新
export const API_DINGLOGIN_URL = '/auth/v1.0/dingLogin' //钉钉授权登录
export const API_DINGAUTHLOGIN_URL = '/auth/v1.0/dingAuthLogin' // 钉钉授权登录authCode
export const API_DINGJSAPI_URL = '/exchange/jsApi/getParams' // 钉钉鉴权
export const API_DING_NOTIFICATION_URL = '/exchange/v1.0/dingTalk/informationAdd' // 钉钉工作通知
export const API_WORK_URL = '/user/configs/selectByConfigCode' //工作台
/* 微信公众号授权登录 */
export const ACCREDIT_CODE_URL = '/exchange/wechat/callback' //根据code获取access_token
export const ACCREDIT_TOKEN_URL = '/auth/wechatlogin' //根据access_token换取真正的token
export const ACCREDIT_SEND_URL = '/exchange/wechat/getCaptchaByWechatBind' //发送验证码
export const ACCREDIT_BINDING_URL = '/exchange/wechat/wechatBind' //绑定手机号
/* 通讯录 */
export const ACCREDIT_ORGANIZATION_URL = '/user/orgs/upAndDown' //根组织接口(个人)
export const ACCREDIT_CONTACTS_URL = '/user/staffOrgs' //个人通讯录接口
/* 公告 */
export const NOTIFY_TOP5PAGE_URL = '/cms/v1.0/notice/selectTop5Page' //首页公告
export const NOTIFY_Tab_URL = '/user/dict/data/list/notice_type_list' //公告Tab
export const NOTIFY_LIST_URL = '/cms/v1.0/notice/selectForPage' //公告列表
export const NOTIFY_DETAILS_URL = '/cms/v1.0/notice/selectNoticeByNoticeId' //公告详情

export const ANNOUCE_TOPPAGE_URL = '/cms/v1.0/advertising/selectTopPage' //广告
export const MENU_ALL_URL = '/user/permissions/findAllMenuVoList' //首页菜单
export const MENU_PERSON_URL = '/user/personalConfig/SysPersonalConfig' //个人主页配置
export const API_KNOWLEDGE_URL = '/cms/v1/document/lib/page' //知识库
/* 微信小程序授权登录 */
export const WECHAT_CALLBACKMP_URL = '/exchange/wechat/callbackMp' //微信小程序回调
export const WECHAT_GETPHONENUMBER_URL = '/exchange/wechat/getPhoneNumber' //获取手机号
export const WECHAT_WECHATBINDMP_URL = '/exchange/wechat/wechatBindMp' //绑定微信unionId

export const API_AGREEMENT_URL = '/user/client/page' //隐私协议 用户协议
export const API_COMMONRESOURCES_URL = '/user/common/clientConfig' //未登录请求的资源

export const API_JPUSHUSER_URL = '/exchange/v1.0/jgDevice/addTagsAndAlias' //极光推送绑定用户

export const API_FILEUPLOAD_URL = '/cms/v1/files/single' // 文件上传
export const API_FILEDOWNLOAD_URL = '/cms/v1/files/downloadGetByPath' // 文件下载
export const API_LAYOUT_DESIGN = '/user/layout-design/getEnableLayoutDesign' // 工作台动态布局

/* 字典 */

export const API_USER_DICT_DATA = '/user/dict/data/list/' //字典查询