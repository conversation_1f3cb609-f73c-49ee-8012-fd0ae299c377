<template>
	<view class="nav-container" :style="{height: navHeightRpx + 'rpx'}">
		<u-navbar safeAreaInsetTop :bgColor="bgImageUrl?'rgba(0, 0, 0, 0)':$attrs.bgColor" v-bind="$attrs"
			:autoBack="true" @leftClick="leftClick" @rightClick="rightClick">
			<!-- #ifdef MP-WEIXIN -->
			<template #left>
				<slot name="left">
					<view class="left-slot">
						<u-icon :name="$attrs.leftIcon ? $attrs.leftIcon : 'arrow-left'"
							:size="$attrs.leftIconSize ? $attrs.leftIconSize : '20'"
							:color="$attrs.leftIconColor ? $attrs.leftIconColor : '#303133'"></u-icon>
						<text
							:style="{ 'color': leftTextColor,'font-size': leftTextSize + 'px' }">{{ $attrs.leftText }}</text>
					</view>
				</slot>
			</template>
			<template #center>
				<slot name="center">
					<view v-if="$attrs.title" class="centerText"
						:style="{width:$attrs.titleWidth/2 + 'px',...$attrs.titleStyle} ">
						{{ $attrs.title }}
					</view>
				</slot>
			</template>
			<template #right>
				<slot name="right">
					<view class="right-slot">
						<u-icon v-if="$attrs.rightIcon" :name="$attrs.rightIcon ? $attrs.rightIcon : ''"
							:size="$attrs.rightIconSize ? $attrs.rightIconSize : '20'"
							:color="$attrs.rightIconColor ? $attrs.rightIconColor : '#303133'"></u-icon>
						<text v-if="$attrs.rightText"
							:style="{ 'color': rightTextColor,'font-size': rightTextSize + 'px' }">{{ $attrs.leftText }}</text>
					</view>
				</slot>
			</template>
			<!-- #endif -->
			<!-- #ifndef MP-WEIXIN -->
			<template v-for="(val, key) in $slots" #[key]>
				<slot :name="key"></slot>
			</template>
			<!-- #endif -->
		</u-navbar>
		<!-- 导航栏背景图，层级高于页面背景图 -->
		<!-- <view class="nav-bar-backgroud" v-if="bgImageUrl">
			<u-image width="100%" mode="top" :height="navHeightRpx + 'rpx'"
				:src="bgImageUrl"></u-image>
		</view> -->
		<!-- 页面背景图 -->
		<view class="nav-backImage" v-if="bgImageUrl">
			<u-image width="100%" mode="top" :height="isFullScreen ? '100vh' : navbarBgImageHeight + 'rpx'"
				:src="bgImageUrl"></u-image>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		useSlots,
		computed,
		useAttrs
	} from 'vue'
	import { onPageScroll } from '@dcloudio/uni-app'
	const props = defineProps({
		// 左边提示文字颜色
		leftTextColor: {
			type: String,
			default: '#000'
		},
		// 左边提示文字大小 默认16，单位：px
		leftTextSize: {
			type: Number,
			default: 16
		},
		// 右边提示文字颜色
		rightTextColor: {
			type: String,
			default: '#000'
		},
		// 右边提示文字大小 默认16，单位：px
		rightTextSize: {
			type: Number,
			default: 16
		},
		// 自定义背景图
		bgImageUrl: {
			type: String,
			default: ''
		},
		// 是否全屏背景
		isFullScreen: {
			type: Boolean,
			default: false
		},
		// 背景图高度 只在bgImageUrl存在时生效,单位px
		bgImageHeight: {
			type: Number,
			default: 0
		}
	})
	const emit = defineEmits(['handlerLeft', 'handlerRight'])
	const leftClick = () => {
		emit('handlerLeft')
	}
	const rightClick = () => {
		emit('handlerRight')
	}

	// 获取父组件传值
	const attribute = useAttrs()
	// 状态栏高度
	const statusHeight = uni.getSystemInfoSync().statusBarHeight
	console.log('状态栏高度',statusHeight);
	// 动态计算背景图高度
	let navHeightRpx = (statusHeight + (attribute.height ? Number(attribute.height) : 44))*2
	console.log('背景高度',attribute.height,navHeightRpx);
	const navbarBgImageHeight = computed(() => {
		if (props.bgImageHeight == 0) {
			// 默认高度
			return navHeightRpx
		} else {
			return props.bgImageHeight*2
		}
	})

	// #ifdef MP-WEIXIN
	// 胶囊位置信息
	let menuButtonInfo = uni.getMenuButtonBoundingClientRect()
	console.log({
		menuButtonInfo
	});
	// #endif
</script>

<style lang="scss" scoped>
	.nav-container {
		width: 100%;
	}

	.nav-backImage {
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 9;
	}
	.nav-bar-backgroud { 
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
	}

	.left-slot,
	.right-slot {
		display: flex;
		flex-wrap: nowrap;
		flex-direction: row;
		align-items: center;
	}

	.centerText {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		// -webkit-line-clamp: 1;
		// display: -webkit-box;
		// -webkit-box-orient: vertical;
	}
</style>