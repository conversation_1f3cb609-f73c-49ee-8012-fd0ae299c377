<template>
  <view class="page">
    <view class="header">
      <text class="title">字体加载器测试</text>
      <button @click="loadFonts" class="load-btn" :disabled="loading">
        {{ loading ? '加载中...' : '重新加载字体' }}
      </button>
    </view>
    
    <view class="status-section">
      <text class="section-title">加载状态</text>
      <view class="status-grid">
        <view class="status-item" :class="{ success: fontStatus.iconfont }">
          <text class="status-label">iconfont:</text>
          <text class="status-value">{{ fontStatus.iconfont ? '✅ 已加载' : '❌ 未加载' }}</text>
        </view>
        <view class="status-item" :class="{ success: fontStatus.iconfonts }">
          <text class="status-label">iconfonts:</text>
          <text class="status-value">{{ fontStatus.iconfonts ? '✅ 已加载' : '❌ 未加载' }}</text>
        </view>
      </view>
    </view>
    
    <view class="test-section" v-if="fontStatus.iconfont || fontStatus.iconfonts">
      <text class="section-title">图标测试</text>
      
      <view class="icon-group" v-if="fontStatus.iconfont">
        <text class="group-label">iconfont 图标:</text>
        <view class="icon-row">
          <text class="icon-item iconfont icon-arrow-right"></text>
          <text class="icon-item iconfont icon-calendar"></text>
          <text class="icon-item iconfont icon-comment"></text>
          <text class="icon-item iconfont icon-file"></text>
        </view>
      </view>
      
      <view class="icon-group" v-if="fontStatus.iconfonts">
        <text class="group-label">iconfonts 图标:</text>
        <view class="icon-row">
          <text class="icon-item iconfonts icon-wodecheliang"></text>
          <text class="icon-item iconfonts icon-shouye"></text>
          <text class="icon-item iconfonts icon-qian"></text>
          <text class="icon-item iconfonts icon-tingchejilu"></text>
        </view>
      </view>
    </view>
    
    <view class="unicode-section">
      <text class="section-title">Unicode 测试</text>
      <view class="unicode-grid">
        <view class="unicode-item">
          <text class="unicode-icon iconfont">&#xe665;</text>
          <text class="unicode-code">e665</text>
        </view>
        <view class="unicode-item">
          <text class="unicode-icon iconfont">&#xe667;</text>
          <text class="unicode-code">e667</text>
        </view>
        <view class="unicode-item">
          <text class="unicode-icon iconfonts">&#xe615;</text>
          <text class="unicode-code">e615</text>
        </view>
        <view class="unicode-item">
          <text class="unicode-icon iconfonts">&#xe639;</text>
          <text class="unicode-code">e639</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import fontLoader from '@/common/utils/fontLoader.js'

const loading = ref(false)
const fontStatus = ref({
  iconfont: false,
  iconfonts: false
})

onMounted(() => {
  console.log('字体加载器测试页面启动')
  loadFonts()
})

async function loadFonts() {
  loading.value = true
  
  try {
    console.log('开始加载字体...')
    const results = await fontLoader.preloadIconFonts()
    
    fontStatus.value = {
      iconfont: results.iconfont,
      iconfonts: results.iconfonts
    }
    
    console.log('字体加载完成:', fontStatus.value)
    
    // 额外检查
    setTimeout(() => {
      checkFontStatus()
    }, 500)
    
  } catch (error) {
    console.error('字体加载失败:', error)
    uni.showToast({
      title: '字体加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

function checkFontStatus() {
  fontStatus.value = {
    iconfont: fontLoader.isFontLoaded('iconfont'),
    iconfonts: fontLoader.isFontLoaded('iconfonts')
  }
  console.log('字体状态检查:', fontStatus.value)
}
</script>

<style lang="scss">
.page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.load-btn {
  background: #c20000;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  
  &[disabled] {
    background: #ccc;
  }
}

.status-section,
.test-section,
.unicode-section {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 10rpx;
}

.status-grid {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
  border-left: 6rpx solid #ddd;
  
  &.success {
    border-left-color: #4cd964;
    background: #f0fff4;
  }
}

.status-label {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.status-value {
  font-size: 28rpx;
  color: #333;
}

.icon-group {
  margin-bottom: 30rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.group-label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.icon-row {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.icon-item {
  font-size: 48rpx;
  color: #c20000;
  padding: 15rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
  border: 2rpx solid #eee;
  text-align: center;
  min-width: 80rpx;
  line-height: 1;
}

.unicode-grid {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
}

.unicode-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background: #f9f9f9;
  border-radius: 8rpx;
  border: 2rpx solid #eee;
}

.unicode-icon {
  font-size: 48rpx;
  color: #c20000;
  margin-bottom: 10rpx;
  line-height: 1;
}

.unicode-code {
  font-size: 24rpx;
  color: #666;
  font-family: monospace;
}

/* 字体样式定义 */
.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal !important;
  font-weight: normal !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iconfonts {
  font-family: 'iconfonts' !important;
  font-style: normal !important;
  font-weight: normal !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标定义 */
.icon-arrow-right:before { content: '\e665'; }
.icon-calendar:before { content: '\e667'; }
.icon-comment:before { content: '\e669'; }
.icon-file:before { content: '\e671'; }
.icon-wodecheliang:before { content: "\e615"; }
.icon-shouye:before { content: "\e639"; }
.icon-qian:before { content: "\e645"; }
.icon-tingchejilu:before { content: "\e602"; }
</style>
