<template>
	<view class="container">
		<view class="files" v-if="accept != 'file'">
			<view class="fileitem" v-for="(item, index) in fileList" :key="item" :class="{ 'bg-video': accept == 'video' }">
				<image class="img-image" v-if="accept == 'image'" :src="item" @click="previewHandler(index, fileList)"></image>
				<image class="img-video" v-if="accept == 'video'"  @click="previewVideo(item)" src="@/static/app-plus/video.png" mode=""></image>
				<image v-if="!props.preview" class="closebtn" src="@/static/app-plus/close.png" @click="deleteHanlder(item)">
				</image>
			</view>
			<view class="imgcell" v-if="!props.preview">
				<image class="select-img" @click="uploadHandler" src="@/static/app-plus/camera.png" width="58rpx"
					height="58rpx"></image>
			</view>
		</view>
		<u-popup class="yu-popup" :show="showVideo" mode="center" @close="close" @open="open">
			<video :src="videoSrc"></video>
			<u-button class="yu-button" @click="close" text="关闭预览"></u-button>
		</u-popup>
		<yuni-chooseImage :ciShow="ciShow" :isVideo="isVideo" :count="props.maxCount" @ciClose="ciClose"
			@ciUpload="ciUpload"></yuni-chooseImage>
	</view>
</template>

<script setup>
	import {
		computed,
		ref,
		watch
	} from 'vue'
	import {
		onLoad,
		onShow
	} from '@dcloudio/uni-app';
	import {
		uploadFile
	} from '@/common/net/request.js'
	import {
		API_FILEUPLOAD_URL
	} from '@/common/net/netUrl.js'
	
	const props = defineProps({
		preview: {
			type: Boolean,
			default: false
		},
		fileList: {
			default: []
		},
		// 图片上传数量限制
		maxCount: {
			type: Number,
			default: 2
		},
		// 接收的文件类型,默认值imag。可选值：image | video | file
		accept: {
			type: String,
			default: 'image'
		}
	})
	const emit = defineEmits(['deleteFiles', 'uploadFiles'])
	
	const isVideo = computed(() => {
		return props.accept == 'video'
	})
	const fileList = ref(props.fileList)
	
	// 视频预览
	const showVideo = ref(false)
	const videoSrc = ref('')
	const previewVideo = (item) => {
		videoSrc.value = item
		open()
	}
	const open = () => {
		showVideo.value = true
	}
	const close = () => {
		showVideo.value = false
	}
	const ciShow = ref(false)
	
	onShow(() => {
		// 选择完毕后关闭页面弹窗
		ciShow.value = false
	})
	const ciClose = () => {
		ciShow.value = false
	}
	const ciUpload = async (res) => {
		console.log('上传成功', res);
		let arr = []
		isVideo.value ? arr.push(res.tempFilePath) : arr = res.tempFilePaths
		let list = await uploadFileList(arr)
		fileList.value = [...fileList.value, ...list]
		emit('uploadFiles', fileList.value)
	}

	// 点击上传
	const uploadHandler = () => {
		let toastText =  `最大支持上传${ props.maxCount  }个${ isVideo.value ? '视频' : '图片' }`
		if(props.maxCount - fileList.value.length <= 0){
			uni.showToast({
				title: toastText,
				icon: 'none'
			})
		} else {
			ciShow.value = true
		}
	}
	// 删除
	const deleteHanlder = (item) => {
		let index = fileList.value.indexOf(item)
		fileList.value.splice(index,1)
		emit('deleteFiles', fileList.value)
	}
	// 预览
	const previewHandler = (index, imgs) => {
		console.log(index, imgs)
		uni.previewImage({
			current: index,
			urls: imgs,
			referrerPolicy: 'origin', // 必填，否则会受到图床的防盗链影响
			success() {
				console.log('预览成功')
			},
		})
	}
	
	// 文件上传接口
	async function uploadFileList(files, names) {
		return new Promise(async (resolve, reject) => {
			let filelist = []
			for (let item of files) {
				try {
					let res = await uploadFile({
						url: API_FILEUPLOAD_URL,
						method: 'POST',
						params: {
							filePath: item
						},
					})
					if (res.success) {
						filelist.push(res.data.url)
					}
				} catch (e) {}
			}
			if (names) {
				resolve({
					files: filelist
				})
			} else {
				resolve(filelist)
			}
		})
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		width: 100%;
	}

	.files {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		width: 100%;
	}

	.imgcell {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 144rpx;
		height: 144rpx;
		border-radius: 6rpx;
		background-color: #F4F5F7;
		margin-bottom: 20rpx;
		
		.select-img {
			width: 58rpx;
			height: 58rpx;
		}
	}
	.bg-video {
		background-color: #000;
	}
	.fileitem {
		width: 144rpx;
		height: 144rpx;
		border: 0.5px solid #E2E2E2;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;

		.img-image {
			width: 100%;
			height: 100%;
		}
		
		.img-video {
			width: 70rpx;
			height: 70rpx;
		}

		.closebtn {
			width: 30rpx;
			height: 30rpx;
			position: absolute;
			top: -16rpx;
			right: -16rpx;
			z-index: 99;
		}
	}
	.yu-popup {
		display: flex;
		justify-content: center;
		align-items: center;
		background: transparent;
	}
</style>